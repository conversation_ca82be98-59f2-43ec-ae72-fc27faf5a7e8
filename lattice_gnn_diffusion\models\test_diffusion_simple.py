"""
扩散模型简化测试：验证代码结构和逻辑（不需要PyTorch）
"""

import sys
import os
import importlib.util

def check_file_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 编译检查语法
        compile(code, file_path, 'exec')
        return True, None
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"其他错误: {e}"

def check_imports(file_path):
    """检查导入语句"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        imports = []
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('import ') or line.startswith('from '):
                imports.append((line_num, line))
        
        return True, imports
    except Exception as e:
        return False, f"读取文件错误: {e}"

def analyze_class_structure(file_path):
    """分析类结构"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        classes = []
        functions = []
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            stripped = line.strip()
            if stripped.startswith('class '):
                class_name = stripped.split('class ')[1].split('(')[0].split(':')[0].strip()
                classes.append((line_num, class_name))
            elif stripped.startswith('def ') and not line.startswith('    '):
                func_name = stripped.split('def ')[1].split('(')[0].strip()
                functions.append((line_num, func_name))
        
        return True, {'classes': classes, 'functions': functions}
    except Exception as e:
        return False, f"分析错误: {e}"

def test_diffusion_core():
    """测试扩散核心模块"""
    print("测试 diffusion_core.py...")
    
    file_path = "diffusion_core.py"
    
    # 1. 语法检查
    success, error = check_file_syntax(file_path)
    if not success:
        print(f"  ❌ 语法检查失败: {error}")
        return False
    print("  ✓ 语法检查通过")
    
    # 2. 导入检查
    success, imports = check_imports(file_path)
    if not success:
        print(f"  ❌ 导入检查失败: {error}")
        return False
    print(f"  ✓ 导入语句检查通过 ({len(imports)} 个导入)")
    
    # 3. 类结构分析
    success, structure = analyze_class_structure(file_path)
    if not success:
        print(f"  ❌ 结构分析失败: {structure}")
        return False
    
    classes = structure['classes']
    functions = structure['functions']
    
    print(f"  ✓ 发现 {len(classes)} 个类:")
    for line_num, class_name in classes:
        print(f"    - {class_name} (行 {line_num})")
    
    print(f"  ✓ 发现 {len(functions)} 个顶级函数:")
    for line_num, func_name in functions:
        print(f"    - {func_name} (行 {line_num})")
    
    # 4. 检查关键类是否存在
    expected_classes = [
        'NoiseScheduler', 'ConditionalUNet', 'ConditionalDDPM', 
        'GraphDiffusionModel', 'EdgePredictor'
    ]
    
    found_classes = [name for _, name in classes]
    missing_classes = [cls for cls in expected_classes if cls not in found_classes]
    
    if missing_classes:
        print(f"  ❌ 缺少关键类: {missing_classes}")
        return False
    
    print("  ✓ 所有关键类都存在")
    return True

def test_conditioning():
    """测试条件模块"""
    print("\n测试 conditioning.py...")
    
    file_path = "conditioning.py"
    
    # 1. 语法检查
    success, error = check_file_syntax(file_path)
    if not success:
        print(f"  ❌ 语法检查失败: {error}")
        return False
    print("  ✓ 语法检查通过")
    
    # 2. 类结构分析
    success, structure = analyze_class_structure(file_path)
    if not success:
        print(f"  ❌ 结构分析失败: {structure}")
        return False
    
    classes = structure['classes']
    print(f"  ✓ 发现 {len(classes)} 个类:")
    for line_num, class_name in classes:
        print(f"    - {class_name} (行 {line_num})")
    
    # 3. 检查关键类
    expected_classes = [
        'PerformanceConditioner', 'AdaptiveConditioner',
        'ConditionalCrossAttention', 'MultiScaleConditioner'
    ]
    
    found_classes = [name for _, name in classes]
    missing_classes = [cls for cls in expected_classes if cls not in found_classes]
    
    if missing_classes:
        print(f"  ❌ 缺少关键类: {missing_classes}")
        return False
    
    print("  ✓ 所有关键类都存在")
    return True

def test_file_completeness():
    """测试文件完整性"""
    print("\n测试文件完整性...")
    
    required_files = [
        "diffusion_core.py",
        "conditioning.py", 
        "test_diffusion.py",
        "diffusion_example.py"
    ]
    
    missing_files = []
    for file_name in required_files:
        if not os.path.exists(file_name):
            missing_files.append(file_name)
        else:
            print(f"  ✓ {file_name} 存在")
    
    if missing_files:
        print(f"  ❌ 缺少文件: {missing_files}")
        return False
    
    print("  ✓ 所有必需文件都存在")
    return True

def analyze_code_metrics():
    """分析代码指标"""
    print("\n代码指标分析...")
    
    files_to_analyze = ["diffusion_core.py", "conditioning.py"]
    
    total_lines = 0
    total_classes = 0
    total_functions = 0
    
    for file_name in files_to_analyze:
        if os.path.exists(file_name):
            with open(file_name, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            file_lines = len(lines)
            total_lines += file_lines
            
            # 统计类和函数
            success, structure = analyze_class_structure(file_name)
            if success:
                classes = len(structure['classes'])
                functions = len(structure['functions'])
                total_classes += classes
                total_functions += functions
                
                print(f"  {file_name}:")
                print(f"    - 代码行数: {file_lines}")
                print(f"    - 类数量: {classes}")
                print(f"    - 函数数量: {functions}")
    
    print(f"\n总计:")
    print(f"  - 总代码行数: {total_lines}")
    print(f"  - 总类数量: {total_classes}")
    print(f"  - 总函数数量: {total_functions}")

def check_documentation():
    """检查文档字符串"""
    print("\n检查文档字符串...")
    
    files_to_check = ["diffusion_core.py", "conditioning.py"]
    
    for file_name in files_to_check:
        if os.path.exists(file_name):
            with open(file_name, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 统计文档字符串
            docstring_count = content.count('"""')
            class_count = content.count('class ')
            function_count = content.count('def ')
            
            print(f"  {file_name}:")
            print(f"    - 文档字符串数量: {docstring_count // 2}")  # 每个文档字符串有开始和结束
            print(f"    - 类数量: {class_count}")
            print(f"    - 函数数量: {function_count}")

def run_all_tests():
    """运行所有测试"""
    print("扩散模型代码结构测试")
    print("=" * 50)
    
    tests = [
        ("文件完整性", test_file_completeness),
        ("扩散核心模块", test_diffusion_core),
        ("条件模块", test_conditioning),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
    
    # 运行分析
    analyze_code_metrics()
    check_documentation()
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！扩散模型实现结构正确。")
        return True
    else:
        print("⚠️  部分测试失败，请检查代码。")
        return False

if __name__ == "__main__":
    run_all_tests()
