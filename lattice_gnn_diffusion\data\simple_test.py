"""
简化的数据测试脚本：不依赖PyTorch，只测试基本数据加载和预处理逻辑
"""

import pandas as pd
import numpy as np
from collections import defaultdict

def test_basic_data_loading():
    """测试基本数据加载"""
    print("=== 测试基本数据加载 ===")
    
    try:
        # 加载数据文件
        nodes_df = pd.read_csv("../../node.csv")
        edges_df = pd.read_csv("../../edge.csv")
        lattice_df = pd.read_csv("../../lattice.csv")
        
        print(f"✓ 成功加载数据文件")
        print(f"  节点数据: {nodes_df.shape}")
        print(f"  边数据: {edges_df.shape}")
        print(f"  点阵数据: {lattice_df.shape}")
        
        # 检查数据一致性
        node_lattices = set(nodes_df['lattice_name'].unique())
        edge_lattices = set(edges_df['lattice_name'].unique())
        lattice_lattices = set(lattice_df['lattice_name'].unique())
        
        common_lattices = node_lattices & edge_lattices & lattice_lattices
        print(f"  共同点阵数量: {len(common_lattices)}")
        
        if len(common_lattices) == len(lattice_lattices):
            print("✓ 数据一致性检查通过")
        else:
            print("✗ 数据一致性检查失败")
            return False
        
        return True, (nodes_df, edges_df, lattice_df, common_lattices)
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        return False, None

def test_graph_construction(nodes_df, edges_df, lattice_name):
    """测试图构建逻辑"""
    print(f"\n=== 测试图构建：{lattice_name} ===")
    
    try:
        # 获取该点阵的节点和边
        lattice_nodes = nodes_df[nodes_df['lattice_name'] == lattice_name].copy()
        lattice_edges = edges_df[edges_df['lattice_name'] == lattice_name].copy()
        
        # 按node_id排序
        lattice_nodes = lattice_nodes.sort_values('node_id')
        
        # 提取节点坐标特征
        node_features = lattice_nodes[['x', 'y', 'z']].values
        print(f"  节点特征形状: {node_features.shape}")
        
        # 构建边索引映射
        node_id_to_idx = {node_id: idx for idx, node_id in enumerate(lattice_nodes['node_id'])}
        
        edge_list = []
        for _, edge in lattice_edges.iterrows():
            src_idx = node_id_to_idx[edge['source_node_original']]
            tgt_idx = node_id_to_idx[edge['target_node_original']]
            edge_list.append([src_idx, tgt_idx])
            edge_list.append([tgt_idx, src_idx])  # 无向图
        
        edge_index = np.array(edge_list).T if edge_list else np.array([[], []])
        print(f"  边索引形状: {edge_index.shape}")
        
        # 验证边索引有效性
        if edge_index.size > 0:
            max_edge_idx = edge_index.max()
            if max_edge_idx >= len(node_features):
                print(f"✗ 边索引超出范围: {max_edge_idx} >= {len(node_features)}")
                return False
        
        print("✓ 图构建成功")
        return True
        
    except Exception as e:
        print(f"✗ 图构建失败: {e}")
        return False

def test_padding_logic(node_features, max_nodes=64):
    """测试padding逻辑"""
    print(f"\n=== 测试Padding逻辑 ===")
    
    try:
        num_nodes = node_features.shape[0]
        
        if num_nodes > max_nodes:
            print(f"✗ 节点数超过最大限制: {num_nodes} > {max_nodes}")
            return False
        
        # 创建mask
        node_mask = np.zeros(max_nodes, dtype=bool)
        node_mask[:num_nodes] = True
        
        # Padding节点特征
        feature_dim = node_features.shape[1]
        padded_features = np.zeros((max_nodes, feature_dim))
        padded_features[:num_nodes] = node_features
        
        print(f"  原始节点数: {num_nodes}")
        print(f"  Padding后形状: {padded_features.shape}")
        print(f"  有效节点mask: {node_mask.sum()}")
        
        # 验证padding正确性
        assert node_mask.sum() == num_nodes, "mask数量不匹配"
        assert np.allclose(padded_features[:num_nodes], node_features), "padding后数据不一致"
        
        print("✓ Padding逻辑正确")
        return True
        
    except Exception as e:
        print(f"✗ Padding测试失败: {e}")
        return False

def test_performance_extraction(lattice_df):
    """测试性能特征提取"""
    print(f"\n=== 测试性能特征提取 ===")
    
    try:
        # 识别性能相关列
        performance_cols = [col for col in lattice_df.columns 
                          if col.startswith(('mech_', 'scaling_'))]
        
        print(f"  识别到 {len(performance_cols)} 个性能特征")
        print(f"  性能特征列: {performance_cols[:5]}...")  # 显示前5个
        
        # 提取数值特征
        performance_matrix = lattice_df[performance_cols].values
        print(f"  性能矩阵形状: {performance_matrix.shape}")
        
        # 检查数据质量
        has_nan = np.isnan(performance_matrix).any()
        has_inf = np.isinf(performance_matrix).any()
        
        if has_nan:
            print("✗ 性能数据包含NaN值")
            return False
        
        if has_inf:
            print("✗ 性能数据包含无穷值")
            return False
        
        print("✓ 性能特征提取成功")
        return True, performance_matrix, performance_cols
        
    except Exception as e:
        print(f"✗ 性能特征提取失败: {e}")
        return False, None, None

def test_data_statistics(nodes_df, edges_df, lattice_df, common_lattices):
    """测试数据统计"""
    print(f"\n=== 数据统计分析 ===")
    
    # 节点数量统计
    node_counts = []
    edge_counts = []
    
    for lattice_name in common_lattices:
        lattice_nodes = nodes_df[nodes_df['lattice_name'] == lattice_name]
        lattice_edges = edges_df[edges_df['lattice_name'] == lattice_name]
        
        node_counts.append(len(lattice_nodes))
        edge_counts.append(len(lattice_edges))
    
    print(f"节点数量统计:")
    print(f"  范围: {min(node_counts)} - {max(node_counts)}")
    print(f"  平均: {np.mean(node_counts):.2f}")
    print(f"  标准差: {np.std(node_counts):.2f}")
    
    print(f"边数量统计:")
    print(f"  范围: {min(edge_counts)} - {max(edge_counts)}")
    print(f"  平均: {np.mean(edge_counts):.2f}")
    print(f"  标准差: {np.std(edge_counts):.2f}")
    
    # 坐标范围统计
    coords = nodes_df[['x', 'y', 'z']].values
    print(f"坐标范围:")
    print(f"  X: [{coords[:, 0].min():.3f}, {coords[:, 0].max():.3f}]")
    print(f"  Y: [{coords[:, 1].min():.3f}, {coords[:, 1].max():.3f}]")
    print(f"  Z: [{coords[:, 2].min():.3f}, {coords[:, 2].max():.3f}]")
    
    return True

def main():
    """主测试函数"""
    print("开始简化数据处理测试...\n")
    
    # 测试基本数据加载
    success, data = test_basic_data_loading()
    if not success:
        print("基本数据加载失败，终止测试")
        return
    
    nodes_df, edges_df, lattice_df, common_lattices = data
    
    # 测试图构建（选择几个样本）
    test_lattices = list(common_lattices)[:3]
    for lattice_name in test_lattices:
        if not test_graph_construction(nodes_df, edges_df, lattice_name):
            print(f"图构建测试失败: {lattice_name}")
            return
    
    # 测试padding逻辑
    sample_lattice = test_lattices[0]
    sample_nodes = nodes_df[nodes_df['lattice_name'] == sample_lattice]
    sample_features = sample_nodes[['x', 'y', 'z']].values
    
    if not test_padding_logic(sample_features):
        print("Padding逻辑测试失败")
        return
    
    # 测试性能特征提取
    success, performance_matrix, performance_cols = test_performance_extraction(lattice_df)
    if not success:
        print("性能特征提取测试失败")
        return
    
    # 数据统计分析
    test_data_statistics(nodes_df, edges_df, lattice_df, common_lattices)
    
    print("\n=== 所有基础测试通过！ ===")
    print("数据处理逻辑正确，可以进行PyTorch集成。")
    print(f"建议安装以下依赖包：")
    print("pip install torch torch-geometric numpy pandas matplotlib seaborn scikit-learn pyyaml tqdm")

if __name__ == "__main__":
    main()
