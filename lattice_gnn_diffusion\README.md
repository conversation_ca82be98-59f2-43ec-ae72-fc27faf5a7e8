# Lattice GNN-Diffusion

A unified framework combining Graph Neural Networks with Diffusion Models for lattice structure prediction and generation.

## Features

- **Dual Functionality**: Both structure-to-performance prediction and performance-to-structure generation
- **Advanced Architecture**: Graph Attention Networks (GAT) + Conditional Diffusion Models
- **Variable-length Graph Support**: Handles graphs with 8-36 nodes using padding and masking
- **Physical Constraints**: Ensures generated structures meet physical requirements
- **25D Performance Prediction**: Multi-target regression for comprehensive material properties

## Quick Start

### Installation

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install package in development mode
pip install -e .
```

### Usage

```python
from lattice_gnn_diffusion import LatticeGNNDiffusion

# Initialize model
model = LatticeGNNDiffusion(config_path="configs/default.yaml")

# Train model
model.train(data_path="path/to/data")

# Predict performance from structure
performance = model.predict_performance(structure)

# Generate structure from performance requirements
structure = model.generate_structure(target_performance)
```

## Project Structure

```
lattice_gnn_diffusion/
├── data/           # Data processing and loading
├── models/         # Neural network architectures
├── training/       # Training loops and optimization
├── utils/          # Utility functions and tools
├── experiments/    # Evaluation and benchmarking
├── interface/      # User interfaces and APIs
├── configs/        # Configuration files
└── docs/           # Documentation
```

## Requirements

- Python >= 3.8
- PyTorch >= 2.0.0
- PyTorch Geometric >= 2.3.0
- See `requirements.txt` for full dependencies

## License

MIT License
