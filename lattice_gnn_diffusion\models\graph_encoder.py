"""
图神经网络编码器：基于GAT的点阵结构编码器
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import global_mean_pool, global_max_pool, global_add_pool
from torch_geometric.data import Batch
from typing import Optional, Tuple, Dict, Any
import math

from .layers import MultiHeadGATLayer, GraphNorm, ResidualBlock, PositionalEncoding

class AttentionPooling(nn.Module):
    """注意力池化层"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 128):
        """
        初始化注意力池化
        
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
        """
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        self.attention = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.Tanh(),
            nn.Linear(hidden_dim, 1)
        )
    
    def forward(self, x: torch.Tensor, batch: Optional[torch.Tensor] = None,
                node_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 节点特征 [num_nodes, input_dim]
            batch: 批索引
            node_mask: 节点mask
        """
        # 计算注意力权重
        attn_weights = self.attention(x)  # [num_nodes, 1]
        
        # 应用节点mask
        if node_mask is not None:
            attn_weights = attn_weights.masked_fill(~node_mask.unsqueeze(-1), float('-inf'))
        
        if batch is None:
            # 单图情况
            attn_weights = F.softmax(attn_weights, dim=0)
            pooled = (x * attn_weights).sum(dim=0)
        else:
            # 批处理情况
            attn_weights = F.softmax(attn_weights, dim=0)  # 简化处理
            pooled = global_add_pool(x * attn_weights.squeeze(-1), batch)
        
        return pooled

class GATEncoder(nn.Module):
    """基于GAT的图编码器"""
    
    def __init__(self, input_dim: int = 3, hidden_dim: int = 128, 
                 output_dim: int = 256, num_layers: int = 4,
                 num_heads: int = 8, dropout: float = 0.1,
                 use_residual: bool = True, use_norm: bool = True,
                 pooling_method: str = 'attention', use_pos_encoding: bool = False):
        """
        初始化GAT编码器
        
        Args:
            input_dim: 输入特征维度（通常为3，表示x,y,z坐标）
            hidden_dim: 隐藏层维度
            output_dim: 输出图嵌入维度
            num_layers: GAT层数
            num_heads: 注意力头数
            dropout: Dropout概率
            use_residual: 是否使用残差连接
            use_norm: 是否使用图归一化
            pooling_method: 池化方法 ('mean', 'max', 'add', 'attention')
            use_pos_encoding: 是否使用位置编码
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.dropout = dropout
        self.use_residual = use_residual
        self.use_norm = use_norm
        self.pooling_method = pooling_method
        self.use_pos_encoding = use_pos_encoding
        
        # 输入投影层
        self.input_proj = nn.Linear(input_dim, hidden_dim)
        
        # 位置编码
        if use_pos_encoding:
            self.pos_encoding = PositionalEncoding(hidden_dim)
        
        # GAT层
        self.gat_layers = nn.ModuleList()
        self.norm_layers = nn.ModuleList()
        
        for i in range(num_layers):
            # GAT层
            if i == 0:
                in_channels = hidden_dim
            else:
                in_channels = hidden_dim
            
            gat_layer = MultiHeadGATLayer(
                in_channels=in_channels,
                out_channels=hidden_dim // num_heads,
                heads=num_heads,
                concat=True,
                dropout=dropout,
                add_self_loops=True
            )
            
            if use_residual:
                gat_layer = ResidualBlock(gat_layer, dropout)
            
            self.gat_layers.append(gat_layer)
            
            # 归一化层
            if use_norm:
                self.norm_layers.append(GraphNorm(hidden_dim))
        
        # 池化层
        if pooling_method == 'attention':
            self.pooling = AttentionPooling(hidden_dim)
        elif pooling_method == 'mean':
            self.pooling = global_mean_pool
        elif pooling_method == 'max':
            self.pooling = global_max_pool
        elif pooling_method == 'add':
            self.pooling = global_add_pool
        else:
            raise ValueError(f"Unknown pooling method: {pooling_method}")
        
        # 输出投影层
        self.output_proj = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim)
        )
        
        # 初始化参数
        self.reset_parameters()
    
    def reset_parameters(self):
        """重置参数"""
        nn.init.xavier_uniform_(self.input_proj.weight)
        nn.init.zeros_(self.input_proj.bias)
        
        for layer in self.output_proj:
            if isinstance(layer, nn.Linear):
                nn.init.xavier_uniform_(layer.weight)
                nn.init.zeros_(layer.bias)
    
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor,
                batch: Optional[torch.Tensor] = None,
                node_mask: Optional[torch.Tensor] = None,
                return_node_embeddings: bool = False) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 节点特征 [num_nodes, input_dim]
            edge_index: 边索引 [2, num_edges]
            batch: 批索引 [num_nodes]
            node_mask: 节点mask [num_nodes]
            return_node_embeddings: 是否返回节点嵌入
            
        Returns:
            图嵌入 [batch_size, output_dim] 或 (图嵌入, 节点嵌入)
        """
        # 输入投影
        h = self.input_proj(x)
        
        # 位置编码
        if self.use_pos_encoding:
            h = self.pos_encoding(h)
        
        # 应用节点mask
        if node_mask is not None:
            h = h * node_mask.unsqueeze(-1).float()
        
        # GAT层
        for i, gat_layer in enumerate(self.gat_layers):
            h = gat_layer(h, edge_index, node_mask=node_mask)
            
            # 归一化
            if self.use_norm and i < len(self.norm_layers):
                h = self.norm_layers[i](h, batch=batch, node_mask=node_mask)
            
            # 激活函数
            h = F.relu(h)
            
            # 应用节点mask
            if node_mask is not None:
                h = h * node_mask.unsqueeze(-1).float()
        
        # 保存节点嵌入
        node_embeddings = h
        
        # 图级别池化
        if self.pooling_method == 'attention':
            graph_embedding = self.pooling(h, batch=batch, node_mask=node_mask)
        else:
            if node_mask is not None:
                # 对于其他池化方法，需要手动处理mask
                h_masked = h * node_mask.unsqueeze(-1).float()
                graph_embedding = self.pooling(h_masked, batch)
            else:
                graph_embedding = self.pooling(h, batch)
        
        # 输出投影
        graph_embedding = self.output_proj(graph_embedding)
        
        if return_node_embeddings:
            return graph_embedding, node_embeddings
        else:
            return graph_embedding
    
    def get_attention_weights(self, x: torch.Tensor, edge_index: torch.Tensor,
                            batch: Optional[torch.Tensor] = None,
                            node_mask: Optional[torch.Tensor] = None,
                            layer_idx: int = -1) -> torch.Tensor:
        """
        获取指定层的注意力权重
        
        Args:
            x: 节点特征
            edge_index: 边索引
            batch: 批索引
            node_mask: 节点mask
            layer_idx: 层索引（-1表示最后一层）
            
        Returns:
            注意力权重
        """
        # 输入投影
        h = self.input_proj(x)
        
        # 位置编码
        if self.use_pos_encoding:
            h = self.pos_encoding(h)
        
        # 前向传播到指定层
        target_layer = layer_idx if layer_idx >= 0 else len(self.gat_layers) + layer_idx
        
        for i, gat_layer in enumerate(self.gat_layers):
            if i == target_layer:
                # 获取注意力权重
                if hasattr(gat_layer, 'layer'):  # ResidualBlock
                    _, attn_weights = gat_layer.layer(h, edge_index, node_mask=node_mask,
                                                    return_attention_weights=True)
                else:
                    _, attn_weights = gat_layer(h, edge_index, node_mask=node_mask,
                                              return_attention_weights=True)
                return attn_weights
            
            # 正常前向传播
            h = gat_layer(h, edge_index, node_mask=node_mask)
            
            if self.use_norm and i < len(self.norm_layers):
                h = self.norm_layers[i](h, batch=batch, node_mask=node_mask)
            
            h = F.relu(h)
            
            if node_mask is not None:
                h = h * node_mask.unsqueeze(-1).float()
        
        return None

class MultiScaleGATEncoder(nn.Module):
    """多尺度GAT编码器"""
    
    def __init__(self, input_dim: int = 3, hidden_dims: list = [64, 128, 256],
                 output_dim: int = 512, num_heads: int = 8, dropout: float = 0.1):
        """
        初始化多尺度GAT编码器
        
        Args:
            input_dim: 输入特征维度
            hidden_dims: 各尺度的隐藏维度
            output_dim: 输出维度
            num_heads: 注意力头数
            dropout: Dropout概率
        """
        super().__init__()
        
        self.encoders = nn.ModuleList()
        
        # 创建不同尺度的编码器
        for hidden_dim in hidden_dims:
            encoder = GATEncoder(
                input_dim=input_dim,
                hidden_dim=hidden_dim,
                output_dim=hidden_dim,
                num_layers=2,
                num_heads=num_heads,
                dropout=dropout
            )
            self.encoders.append(encoder)
        
        # 融合层
        total_dim = sum(hidden_dims)
        self.fusion = nn.Sequential(
            nn.Linear(total_dim, output_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim, output_dim)
        )
    
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor,
                batch: Optional[torch.Tensor] = None,
                node_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播"""
        embeddings = []
        
        # 各尺度编码
        for encoder in self.encoders:
            emb = encoder(x, edge_index, batch, node_mask)
            embeddings.append(emb)
        
        # 融合
        combined = torch.cat(embeddings, dim=-1)
        output = self.fusion(combined)
        
        return output
