"""
数据分析脚本：深入分析点阵结构数据的特征和分布
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

def analyze_node_data(node_file="../../node.csv"):
    """分析节点数据"""
    print("=== 节点数据分析 ===")

    # 加载数据
    nodes_df = pd.read_csv(node_file)
    print(f"节点数据形状: {nodes_df.shape}")
    print(f"列名: {list(nodes_df.columns)}")
    
    # 基本统计
    print("\n基本统计信息:")
    print(nodes_df.describe())
    
    # 检查缺失值
    print(f"\n缺失值检查:")
    print(nodes_df.isnull().sum())
    
    # 分析每个点阵结构的节点数量
    node_counts = nodes_df.groupby('lattice_name').size()
    print(f"\n点阵结构数量: {len(node_counts)}")
    print(f"节点数量范围: {node_counts.min()} - {node_counts.max()}")
    print(f"平均节点数: {node_counts.mean():.2f}")
    
    # 坐标范围分析
    print(f"\nX坐标范围: [{nodes_df['x'].min():.3f}, {nodes_df['x'].max():.3f}]")
    print(f"Y坐标范围: [{nodes_df['y'].min():.3f}, {nodes_df['y'].max():.3f}]")
    print(f"Z坐标范围: [{nodes_df['z'].min():.3f}, {nodes_df['z'].max():.3f}]")
    
    return nodes_df, node_counts

def analyze_edge_data(edge_file="../../edge.csv"):
    """分析边数据"""
    print("\n=== 边数据分析 ===")

    # 加载数据
    edges_df = pd.read_csv(edge_file)
    print(f"边数据形状: {edges_df.shape}")
    print(f"列名: {list(edges_df.columns)}")
    
    # 基本统计
    print("\n基本统计信息:")
    print(edges_df.describe())
    
    # 检查缺失值
    print(f"\n缺失值检查:")
    print(edges_df.isnull().sum())
    
    # 分析每个点阵结构的边数量
    edge_counts = edges_df.groupby('lattice_name').size()
    print(f"\n边数量范围: {edge_counts.min()} - {edge_counts.max()}")
    print(f"平均边数: {edge_counts.mean():.2f}")
    
    # 分析节点度分布
    degree_stats = defaultdict(list)
    for lattice_name in edges_df['lattice_name'].unique():
        lattice_edges = edges_df[edges_df['lattice_name'] == lattice_name]
        
        # 计算每个节点的度
        all_nodes = list(lattice_edges['source_node_original']) + list(lattice_edges['target_node_original'])
        node_degrees = pd.Series(all_nodes).value_counts()
        
        degree_stats['lattice'].append(lattice_name)
        degree_stats['avg_degree'].append(node_degrees.mean())
        degree_stats['max_degree'].append(node_degrees.max())
        degree_stats['min_degree'].append(node_degrees.min())
    
    degree_df = pd.DataFrame(degree_stats)
    print(f"\n度统计:")
    print(f"平均度范围: {degree_df['avg_degree'].min():.2f} - {degree_df['avg_degree'].max():.2f}")
    print(f"最大度范围: {degree_df['min_degree'].min()} - {degree_df['max_degree'].max()}")
    
    return edges_df, edge_counts, degree_df

def analyze_lattice_data(lattice_file="../../lattice.csv"):
    """分析点阵性能数据"""
    print("\n=== 点阵性能数据分析 ===")

    # 加载数据
    lattice_df = pd.read_csv(lattice_file)
    print(f"点阵数据形状: {lattice_df.shape}")
    print(f"列名: {list(lattice_df.columns)}")
    
    # 基本统计
    print("\n基本统计信息:")
    print(lattice_df.describe())
    
    # 检查缺失值
    print(f"\n缺失值检查:")
    print(lattice_df.isnull().sum())
    
    # 分析性能指标
    performance_cols = [col for col in lattice_df.columns if col.startswith(('mech_', 'scaling_'))]
    print(f"\n性能指标数量: {len(performance_cols)}")
    print("性能指标列名:")
    for i, col in enumerate(performance_cols, 1):
        print(f"{i:2d}. {col}")
    
    # 分析数值范围和分布
    print(f"\n性能指标数值范围:")
    for col in performance_cols[:10]:  # 显示前10个
        values = lattice_df[col]
        print(f"{col:15s}: [{values.min():.2e}, {values.max():.2e}] (std: {values.std():.2e})")
    
    # 检查重叠条的分布
    overlap_dist = lattice_df['has_overlapping_bars'].value_counts()
    print(f"\n重叠条分布:")
    print(overlap_dist)
    
    return lattice_df, performance_cols

def analyze_data_consistency(nodes_df, edges_df, lattice_df):
    """分析数据一致性"""
    print("\n=== 数据一致性检查 ===")
    
    # 检查点阵名称一致性
    node_lattices = set(nodes_df['lattice_name'].unique())
    edge_lattices = set(edges_df['lattice_name'].unique())
    lattice_lattices = set(lattice_df['lattice_name'].unique())
    
    print(f"节点文件中的点阵数量: {len(node_lattices)}")
    print(f"边文件中的点阵数量: {len(edge_lattices)}")
    print(f"性能文件中的点阵数量: {len(lattice_lattices)}")
    
    # 检查交集
    common_lattices = node_lattices & edge_lattices & lattice_lattices
    print(f"三个文件共同的点阵数量: {len(common_lattices)}")
    
    if len(common_lattices) != len(lattice_lattices):
        print("警告：数据文件间存在不一致！")
        print(f"仅在节点文件中: {node_lattices - common_lattices}")
        print(f"仅在边文件中: {edge_lattices - common_lattices}")
        print(f"仅在性能文件中: {lattice_lattices - common_lattices}")
    
    # 验证节点和边数量的一致性
    print(f"\n节点和边数量一致性检查:")
    for lattice_name in list(common_lattices)[:5]:  # 检查前5个
        node_count = len(nodes_df[nodes_df['lattice_name'] == lattice_name])
        edge_count = len(edges_df[edges_df['lattice_name'] == lattice_name])
        lattice_info = lattice_df[lattice_df['lattice_name'] == lattice_name].iloc[0]
        
        print(f"{lattice_name}: 节点{node_count} vs 记录{lattice_info['num_nodes']}, "
              f"边{edge_count} vs 记录{lattice_info['num_edges']}")
    
    return common_lattices

def main():
    """主分析函数"""
    print("开始数据分析...")
    
    # 分析各个数据文件
    nodes_df, node_counts = analyze_node_data()
    edges_df, edge_counts, degree_df = analyze_edge_data()
    lattice_df, performance_cols = analyze_lattice_data()
    
    # 数据一致性检查
    common_lattices = analyze_data_consistency(nodes_df, edges_df, lattice_df)
    
    print(f"\n=== 总结 ===")
    print(f"有效点阵结构数量: {len(common_lattices)}")
    print(f"节点数量范围: {node_counts.min()} - {node_counts.max()}")
    print(f"边数量范围: {edge_counts.min()} - {edge_counts.max()}")
    print(f"性能指标维度: {len(performance_cols)}")
    print("数据质量：无缺失值，结构完整")
    
    return {
        'nodes_df': nodes_df,
        'edges_df': edges_df,
        'lattice_df': lattice_df,
        'common_lattices': common_lattices,
        'performance_cols': performance_cols,
        'node_counts': node_counts,
        'edge_counts': edge_counts
    }

if __name__ == "__main__":
    results = main()
