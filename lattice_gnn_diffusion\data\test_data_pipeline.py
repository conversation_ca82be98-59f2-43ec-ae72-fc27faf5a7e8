"""
测试数据处理管道的完整性和正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from data.dataset import LatticeDataModule
from data.transforms import get_training_transforms, get_validation_transforms
import numpy as np

def test_data_loading():
    """测试数据加载功能"""
    print("=== 测试数据加载 ===")
    
    # 初始化数据模块
    data_module = LatticeDataModule(
        node_file="../../node.csv",
        edge_file="../../edge.csv",
        lattice_file="../../lattice.csv",
        max_nodes=64,
        batch_size=4,
        train_ratio=0.7,
        val_ratio=0.15,
        test_ratio=0.15,
        random_seed=42
    )
    
    # 准备数据
    try:
        data_module.prepare_data()
        print("✓ 数据加载成功")
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        return False
    
    # 设置数据集分割
    try:
        data_module.setup()
        print("✓ 数据集分割成功")
    except Exception as e:
        print(f"✗ 数据集分割失败: {e}")
        return False
    
    # 获取统计信息
    stats = data_module.get_statistics()
    print(f"数据集统计信息:")
    print(f"  总样本数: {stats['full']['num_samples']}")
    print(f"  训练集: {stats['train']['num_samples']}")
    print(f"  验证集: {stats['val']['num_samples']}")
    print(f"  测试集: {stats['test']['num_samples']}")
    print(f"  节点数范围: {stats['full']['node_count_range']}")
    print(f"  边数范围: {stats['full']['edge_count_range']}")
    print(f"  特征维度: {stats['full']['feature_dim']}")
    print(f"  目标维度: {stats['full']['target_dim']}")
    
    return True, data_module

def test_data_loaders(data_module):
    """测试数据加载器"""
    print("\n=== 测试数据加载器 ===")
    
    try:
        # 获取数据加载器
        train_loader = data_module.train_dataloader()
        val_loader = data_module.val_dataloader()
        test_loader = data_module.test_dataloader()
        
        print(f"训练加载器批次数: {len(train_loader)}")
        print(f"验证加载器批次数: {len(val_loader)}")
        print(f"测试加载器批次数: {len(test_loader)}")
        
        # 测试一个批次
        batch = next(iter(train_loader))
        print(f"批次数据形状:")
        print(f"  节点特征: {batch.x.shape}")
        print(f"  边索引: {batch.edge_index.shape}")
        print(f"  目标: {batch.y.shape}")
        print(f"  节点mask: {batch.node_mask.shape}")
        print(f"  批大小: {batch.batch.max().item() + 1}")
        
        # 验证数据类型
        assert batch.x.dtype == torch.float32, "节点特征应为float32"
        assert batch.edge_index.dtype == torch.long, "边索引应为long"
        assert batch.y.dtype == torch.float32, "目标应为float32"
        assert batch.node_mask.dtype == torch.bool, "节点mask应为bool"
        
        print("✓ 数据加载器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据加载器测试失败: {e}")
        return False

def test_data_transforms():
    """测试数据变换"""
    print("\n=== 测试数据变换 ===")
    
    try:
        # 创建测试数据
        test_data = torch.randn(10, 3)  # 10个节点，3维坐标
        edge_index = torch.tensor([[0, 1, 2], [1, 2, 3]], dtype=torch.long)
        node_mask = torch.ones(10, dtype=torch.bool)
        
        from torch_geometric.data import Data
        data = Data(x=test_data, edge_index=edge_index, node_mask=node_mask)
        
        # 测试训练变换
        train_transform = get_training_transforms()
        transformed_data = train_transform(data)
        
        print(f"原始数据形状: {data.x.shape}")
        print(f"变换后数据形状: {transformed_data.x.shape}")
        print(f"坐标范围变化: {data.x[:, :3].abs().max():.3f} -> {transformed_data.x[:, :3].abs().max():.3f}")
        
        # 测试验证变换
        val_transform = get_validation_transforms()
        val_transformed_data = val_transform(data)
        
        print("✓ 数据变换测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据变换测试失败: {e}")
        return False

def test_graph_properties(data_module):
    """测试图属性"""
    print("\n=== 测试图属性 ===")
    
    try:
        # 获取一个样本
        sample = data_module.full_dataset[0]
        
        print(f"样本属性:")
        print(f"  点阵名称: {sample.lattice_name}")
        print(f"  实际节点数: {sample.num_nodes}")
        print(f"  padding后节点数: {sample.x.shape[0]}")
        print(f"  边数: {sample.edge_index.shape[1]}")
        print(f"  有效节点数: {sample.node_mask.sum().item()}")
        
        # 验证mask的正确性
        assert sample.node_mask.sum().item() == sample.num_nodes, "节点mask与实际节点数不匹配"
        
        # 验证边索引的有效性
        if sample.edge_index.shape[1] > 0:
            max_edge_idx = sample.edge_index.max().item()
            assert max_edge_idx < sample.num_nodes, f"边索引超出有效节点范围: {max_edge_idx} >= {sample.num_nodes}"
        
        # 验证性能标签
        assert sample.y.shape[0] > 0, "性能标签维度应大于0"
        assert not torch.isnan(sample.y).any(), "性能标签不应包含NaN"
        
        print("✓ 图属性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 图属性测试失败: {e}")
        return False

def test_data_consistency():
    """测试数据一致性"""
    print("\n=== 测试数据一致性 ===")
    
    try:
        # 创建两个相同配置的数据模块
        data_module1 = LatticeDataModule(
            node_file="../../node.csv",
            edge_file="../../edge.csv",
            lattice_file="../../lattice.csv",
            random_seed=42
        )

        data_module2 = LatticeDataModule(
            node_file="../../node.csv",
            edge_file="../../edge.csv",
            lattice_file="../../lattice.csv",
            random_seed=42
        )
        
        data_module1.setup()
        data_module2.setup()
        
        # 验证数据一致性
        assert len(data_module1.train_dataset) == len(data_module2.train_dataset), "训练集大小不一致"
        assert len(data_module1.val_dataset) == len(data_module2.val_dataset), "验证集大小不一致"
        assert len(data_module1.test_dataset) == len(data_module2.test_dataset), "测试集大小不一致"
        
        # 验证第一个样本的一致性
        sample1 = data_module1.train_dataset[0]
        sample2 = data_module2.train_dataset[0]
        
        assert sample1.lattice_name == sample2.lattice_name, "样本点阵名称不一致"
        assert torch.allclose(sample1.y, sample2.y), "样本性能标签不一致"
        
        print("✓ 数据一致性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据一致性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始数据处理管道测试...\n")
    
    # 测试数据加载
    success, data_module = test_data_loading()
    if not success:
        print("数据加载测试失败，终止测试")
        return
    
    # 测试数据加载器
    if not test_data_loaders(data_module):
        print("数据加载器测试失败")
        return
    
    # 测试数据变换
    if not test_data_transforms():
        print("数据变换测试失败")
        return
    
    # 测试图属性
    if not test_graph_properties(data_module):
        print("图属性测试失败")
        return
    
    # 测试数据一致性
    if not test_data_consistency():
        print("数据一致性测试失败")
        return
    
    print("\n=== 所有测试通过！ ===")
    print("数据处理管道工作正常，可以进行模型训练。")

if __name__ == "__main__":
    main()
