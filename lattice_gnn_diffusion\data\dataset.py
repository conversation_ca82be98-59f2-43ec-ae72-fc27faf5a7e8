"""
数据集类：PyTorch Geometric兼容的点阵结构数据集
"""

import torch
from torch_geometric.data import Dataset, Data, DataLoader
from torch.utils.data import random_split
import os
import pickle
from typing import List, Tuple, Optional, Dict
import numpy as np
from .preprocessing import LatticeDataPreprocessor

class LatticeDataset(Dataset):
    """点阵结构数据集"""
    
    def __init__(self, data_list: List[Data], transform=None, pre_transform=None):
        """
        初始化数据集
        
        Args:
            data_list: PyTorch Geometric Data对象列表
            transform: 数据变换函数
            pre_transform: 预处理变换函数
        """
        super().__init__(transform=transform, pre_transform=pre_transform)
        self.data_list = data_list
        
    def len(self):
        """返回数据集大小"""
        return len(self.data_list)
    
    def get(self, idx):
        """获取单个数据样本"""
        data = self.data_list[idx]
        
        if self.transform is not None:
            data = self.transform(data)
            
        return data
    
    def get_statistics(self) -> Dict:
        """获取数据集统计信息"""
        if not self.data_list:
            return {}
        
        node_counts = [data.num_nodes for data in self.data_list]
        edge_counts = [data.edge_index.shape[1] for data in self.data_list]
        feature_dims = [data.x.shape[1] for data in self.data_list]
        target_dims = [data.y.shape[0] for data in self.data_list]
        
        return {
            'num_samples': len(self.data_list),
            'node_count_range': (min(node_counts), max(node_counts)),
            'edge_count_range': (min(edge_counts), max(edge_counts)),
            'avg_nodes': np.mean(node_counts),
            'avg_edges': np.mean(edge_counts),
            'feature_dim': feature_dims[0] if feature_dims else 0,
            'target_dim': target_dims[0] if target_dims else 0,
            'max_nodes': max([data.x.shape[0] for data in self.data_list])
        }

class LatticeDataModule:
    """数据模块：处理数据加载、预处理和分割"""
    
    def __init__(self, node_file: str, edge_file: str, lattice_file: str,
                 max_nodes: int = 64, batch_size: int = 8,
                 train_ratio: float = 0.7, val_ratio: float = 0.15,
                 test_ratio: float = 0.15, random_seed: int = 42,
                 normalize_coords: bool = True, normalize_performance: bool = True,
                 cache_dir: Optional[str] = None):
        """
        初始化数据模块
        
        Args:
            node_file: 节点数据文件路径
            edge_file: 边数据文件路径
            lattice_file: 点阵性能数据文件路径
            max_nodes: 最大节点数
            batch_size: 批大小
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            test_ratio: 测试集比例
            random_seed: 随机种子
            normalize_coords: 是否标准化坐标
            normalize_performance: 是否标准化性能
            cache_dir: 缓存目录
        """
        self.node_file = node_file
        self.edge_file = edge_file
        self.lattice_file = lattice_file
        self.max_nodes = max_nodes
        self.batch_size = batch_size
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        self.random_seed = random_seed
        self.cache_dir = cache_dir
        
        # 验证比例
        if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
            raise ValueError("训练、验证、测试比例之和必须为1.0")
        
        # 初始化预处理器
        self.preprocessor = LatticeDataPreprocessor(
            max_nodes=max_nodes,
            normalize_coords=normalize_coords,
            normalize_performance=normalize_performance
        )
        
        # 数据集
        self.train_dataset = None
        self.val_dataset = None
        self.test_dataset = None
        self.full_dataset = None
        
        # 统计信息
        self.statistics = {}
        
    def _get_cache_path(self) -> str:
        """获取缓存文件路径"""
        if self.cache_dir is None:
            return None
        
        os.makedirs(self.cache_dir, exist_ok=True)
        cache_name = f"lattice_data_maxnodes{self.max_nodes}_norm{self.preprocessor.normalize_coords}_{self.preprocessor.normalize_performance}.pkl"
        return os.path.join(self.cache_dir, cache_name)
    
    def _load_from_cache(self) -> Optional[List[Data]]:
        """从缓存加载数据"""
        cache_path = self._get_cache_path()
        if cache_path and os.path.exists(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    cached_data = pickle.load(f)
                print(f"从缓存加载数据: {cache_path}")
                return cached_data['data_list'], cached_data['preprocessor']
            except Exception as e:
                print(f"缓存加载失败: {e}")
        return None, None
    
    def _save_to_cache(self, data_list: List[Data]):
        """保存数据到缓存"""
        cache_path = self._get_cache_path()
        if cache_path:
            try:
                cached_data = {
                    'data_list': data_list,
                    'preprocessor': self.preprocessor
                }
                with open(cache_path, 'wb') as f:
                    pickle.dump(cached_data, f)
                print(f"数据已缓存到: {cache_path}")
            except Exception as e:
                print(f"缓存保存失败: {e}")
    
    def prepare_data(self):
        """准备数据：加载、预处理、缓存"""
        print("准备数据...")
        
        # 尝试从缓存加载
        cached_data, cached_preprocessor = self._load_from_cache()
        if cached_data is not None:
            self.full_dataset = LatticeDataset(cached_data)
            self.preprocessor = cached_preprocessor
            print(f"从缓存加载了 {len(cached_data)} 个样本")
            return
        
        # 加载原始数据
        data_dict = self.preprocessor.load_data(
            self.node_file, self.edge_file, self.lattice_file)
        
        # 拟合标准化器
        self.preprocessor.fit_scalers(data_dict)
        
        # 处理所有点阵
        processed_data = self.preprocessor.process_all_lattices(data_dict)
        
        # 创建数据集
        self.full_dataset = LatticeDataset(processed_data)
        
        # 保存到缓存
        self._save_to_cache(processed_data)
        
        print(f"数据准备完成，共 {len(processed_data)} 个样本")
    
    def setup(self):
        """设置数据集分割"""
        if self.full_dataset is None:
            self.prepare_data()
        
        # 设置随机种子
        torch.manual_seed(self.random_seed)
        
        # 计算分割大小
        total_size = len(self.full_dataset)
        train_size = int(total_size * self.train_ratio)
        val_size = int(total_size * self.val_ratio)
        test_size = total_size - train_size - val_size
        
        # 分割数据集
        train_data, val_data, test_data = random_split(
            self.full_dataset, [train_size, val_size, test_size])
        
        self.train_dataset = LatticeDataset(
            [self.full_dataset.data_list[i] for i in train_data.indices])
        self.val_dataset = LatticeDataset(
            [self.full_dataset.data_list[i] for i in val_data.indices])
        self.test_dataset = LatticeDataset(
            [self.full_dataset.data_list[i] for i in test_data.indices])
        
        print(f"数据集分割完成: 训练{len(self.train_dataset)}, "
              f"验证{len(self.val_dataset)}, 测试{len(self.test_dataset)}")
        
        # 更新统计信息
        self.statistics = {
            'full': self.full_dataset.get_statistics(),
            'train': self.train_dataset.get_statistics(),
            'val': self.val_dataset.get_statistics(),
            'test': self.test_dataset.get_statistics(),
            'preprocessor': self.preprocessor.get_statistics()
        }
    
    def train_dataloader(self) -> DataLoader:
        """获取训练数据加载器"""
        if self.train_dataset is None:
            self.setup()
        return DataLoader(self.train_dataset, batch_size=self.batch_size, 
                         shuffle=True, num_workers=0)
    
    def val_dataloader(self) -> DataLoader:
        """获取验证数据加载器"""
        if self.val_dataset is None:
            self.setup()
        return DataLoader(self.val_dataset, batch_size=self.batch_size, 
                         shuffle=False, num_workers=0)
    
    def test_dataloader(self) -> DataLoader:
        """获取测试数据加载器"""
        if self.test_dataset is None:
            self.setup()
        return DataLoader(self.test_dataset, batch_size=self.batch_size, 
                         shuffle=False, num_workers=0)
    
    def get_feature_dims(self) -> Tuple[int, int]:
        """获取特征维度"""
        if self.full_dataset is None:
            self.prepare_data()
        
        sample = self.full_dataset[0]
        return sample.x.shape[1], sample.y.shape[0]  # 节点特征维度, 性能维度
    
    def get_statistics(self) -> Dict:
        """获取数据统计信息"""
        if not self.statistics:
            self.setup()
        return self.statistics
