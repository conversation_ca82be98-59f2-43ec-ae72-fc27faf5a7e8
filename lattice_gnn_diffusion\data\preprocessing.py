"""
数据预处理模块：处理点阵结构数据，转换为PyTorch Geometric格式
"""

import pandas as pd
import numpy as np
import torch
from torch_geometric.data import Data
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class LatticeDataPreprocessor:
    """点阵数据预处理器"""
    
    def __init__(self, max_nodes: int = 64, normalize_coords: bool = True, 
                 normalize_performance: bool = True):
        """
        初始化预处理器
        
        Args:
            max_nodes: 最大节点数（用于padding）
            normalize_coords: 是否标准化坐标
            normalize_performance: 是否标准化性能指标
        """
        self.max_nodes = max_nodes
        self.normalize_coords = normalize_coords
        self.normalize_performance = normalize_performance
        
        # 标准化器
        self.coord_scaler = StandardScaler() if normalize_coords else None
        self.performance_scaler = StandardScaler() if normalize_performance else None
        
        # 数据统计信息
        self.node_counts = {}
        self.edge_counts = {}
        self.performance_stats = {}
        
    def load_data(self, node_file: str, edge_file: str, lattice_file: str) -> Dict:
        """
        加载原始CSV数据
        
        Args:
            node_file: 节点数据文件路径
            edge_file: 边数据文件路径  
            lattice_file: 点阵性能数据文件路径
            
        Returns:
            包含所有数据的字典
        """
        print("加载数据文件...")
        
        # 加载数据
        nodes_df = pd.read_csv(node_file)
        edges_df = pd.read_csv(edge_file)
        lattice_df = pd.read_csv(lattice_file)
        
        # 数据质量检查
        self._validate_data(nodes_df, edges_df, lattice_df)
        
        # 获取共同的点阵名称
        common_lattices = self._get_common_lattices(nodes_df, edges_df, lattice_df)
        
        print(f"成功加载 {len(common_lattices)} 个点阵结构")
        
        return {
            'nodes_df': nodes_df,
            'edges_df': edges_df,
            'lattice_df': lattice_df,
            'common_lattices': common_lattices
        }
    
    def _validate_data(self, nodes_df: pd.DataFrame, edges_df: pd.DataFrame, 
                      lattice_df: pd.DataFrame):
        """数据质量验证"""
        # 检查缺失值
        for name, df in [('nodes', nodes_df), ('edges', edges_df), ('lattice', lattice_df)]:
            if df.isnull().sum().sum() > 0:
                raise ValueError(f"{name} 数据存在缺失值")
        
        # 检查必要列
        required_node_cols = ['lattice_name', 'node_id', 'x', 'y', 'z']
        required_edge_cols = ['lattice_name', 'source_node_original', 'target_node_original']
        required_lattice_cols = ['lattice_name', 'num_nodes', 'num_edges']
        
        for cols, df, name in [(required_node_cols, nodes_df, 'nodes'),
                              (required_edge_cols, edges_df, 'edges'),
                              (required_lattice_cols, lattice_df, 'lattice')]:
            missing_cols = set(cols) - set(df.columns)
            if missing_cols:
                raise ValueError(f"{name} 数据缺少必要列: {missing_cols}")
    
    def _get_common_lattices(self, nodes_df: pd.DataFrame, edges_df: pd.DataFrame,
                           lattice_df: pd.DataFrame) -> List[str]:
        """获取三个文件共同的点阵名称"""
        node_lattices = set(nodes_df['lattice_name'].unique())
        edge_lattices = set(edges_df['lattice_name'].unique())
        lattice_lattices = set(lattice_df['lattice_name'].unique())
        
        common = node_lattices & edge_lattices & lattice_lattices
        return sorted(list(common))
    
    def extract_performance_features(self, lattice_df: pd.DataFrame) -> Tuple[np.ndarray, List[str]]:
        """提取性能特征"""
        # 识别性能相关列
        performance_cols = [col for col in lattice_df.columns 
                          if col.startswith(('mech_', 'scaling_'))]
        
        # 提取数值特征
        performance_matrix = lattice_df[performance_cols].values
        
        print(f"提取了 {len(performance_cols)} 个性能特征")
        
        return performance_matrix, performance_cols
    
    def build_graph_from_lattice(self, lattice_name: str, nodes_df: pd.DataFrame,
                               edges_df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        从单个点阵构建图数据
        
        Args:
            lattice_name: 点阵名称
            nodes_df: 节点数据
            edges_df: 边数据
            
        Returns:
            节点特征矩阵和边索引
        """
        # 获取该点阵的节点和边
        lattice_nodes = nodes_df[nodes_df['lattice_name'] == lattice_name].copy()
        lattice_edges = edges_df[edges_df['lattice_name'] == lattice_name].copy()
        
        # 按node_id排序
        lattice_nodes = lattice_nodes.sort_values('node_id')
        
        # 提取节点坐标特征
        node_features = lattice_nodes[['x', 'y', 'z']].values
        
        # 构建边索引（转换为0-based索引）
        node_id_to_idx = {node_id: idx for idx, node_id in enumerate(lattice_nodes['node_id'])}
        
        edge_index = []
        for _, edge in lattice_edges.iterrows():
            src_idx = node_id_to_idx[edge['source_node_original']]
            tgt_idx = node_id_to_idx[edge['target_node_original']]
            edge_index.append([src_idx, tgt_idx])
            edge_index.append([tgt_idx, src_idx])  # 无向图
        
        edge_index = np.array(edge_index).T if edge_index else np.array([[], []])
        
        return node_features, edge_index
    
    def pad_graph(self, node_features: np.ndarray, edge_index: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        将图padding到固定大小
        
        Args:
            node_features: 节点特征 [num_nodes, feature_dim]
            edge_index: 边索引 [2, num_edges]
            
        Returns:
            padded节点特征、padded边索引、节点mask
        """
        num_nodes = node_features.shape[0]
        
        if num_nodes > self.max_nodes:
            raise ValueError(f"节点数 {num_nodes} 超过最大限制 {self.max_nodes}")
        
        # 创建mask
        node_mask = np.zeros(self.max_nodes, dtype=bool)
        node_mask[:num_nodes] = True
        
        # Padding节点特征
        feature_dim = node_features.shape[1]
        padded_features = np.zeros((self.max_nodes, feature_dim))
        padded_features[:num_nodes] = node_features
        
        # 边索引不需要padding，保持原样
        padded_edge_index = edge_index
        
        return padded_features, padded_edge_index, node_mask
    
    def fit_scalers(self, data_dict: Dict):
        """拟合标准化器"""
        nodes_df = data_dict['nodes_df']
        lattice_df = data_dict['lattice_df']
        
        if self.normalize_coords:
            # 拟合坐标标准化器
            coords = nodes_df[['x', 'y', 'z']].values
            self.coord_scaler.fit(coords)
            print("坐标标准化器已拟合")
        
        if self.normalize_performance:
            # 拟合性能标准化器
            performance_matrix, _ = self.extract_performance_features(lattice_df)
            self.performance_scaler.fit(performance_matrix)
            print("性能标准化器已拟合")
    
    def process_all_lattices(self, data_dict: Dict) -> List[Data]:
        """
        处理所有点阵结构，转换为PyTorch Geometric格式
        
        Args:
            data_dict: 包含所有原始数据的字典
            
        Returns:
            PyTorch Geometric Data对象列表
        """
        nodes_df = data_dict['nodes_df']
        edges_df = data_dict['edges_df']
        lattice_df = data_dict['lattice_df']
        common_lattices = data_dict['common_lattices']
        
        # 提取性能特征
        performance_matrix, performance_cols = self.extract_performance_features(lattice_df)
        
        # 标准化性能特征
        if self.normalize_performance and self.performance_scaler:
            performance_matrix = self.performance_scaler.transform(performance_matrix)
        
        # 创建点阵名称到性能的映射
        lattice_to_performance = dict(zip(lattice_df['lattice_name'], performance_matrix))
        
        processed_data = []
        
        print(f"开始处理 {len(common_lattices)} 个点阵结构...")
        
        for i, lattice_name in enumerate(common_lattices):
            try:
                # 构建图
                node_features, edge_index = self.build_graph_from_lattice(
                    lattice_name, nodes_df, edges_df)
                
                # 标准化坐标
                if self.normalize_coords and self.coord_scaler:
                    node_features = self.coord_scaler.transform(node_features)
                
                # Padding
                padded_features, padded_edge_index, node_mask = self.pad_graph(
                    node_features, edge_index)
                
                # 获取性能标签
                performance = lattice_to_performance[lattice_name]
                
                # 创建PyTorch Geometric Data对象
                data = Data(
                    x=torch.FloatTensor(padded_features),
                    edge_index=torch.LongTensor(padded_edge_index),
                    y=torch.FloatTensor(performance),
                    node_mask=torch.BoolTensor(node_mask),
                    lattice_name=lattice_name,
                    num_nodes=len(node_features)
                )
                
                processed_data.append(data)
                
                # 记录统计信息
                self.node_counts[lattice_name] = len(node_features)
                self.edge_counts[lattice_name] = edge_index.shape[1] if edge_index.size > 0 else 0
                
            except Exception as e:
                print(f"处理 {lattice_name} 时出错: {e}")
                continue
        
        print(f"成功处理 {len(processed_data)} 个点阵结构")
        
        return processed_data
    
    def get_statistics(self) -> Dict:
        """获取数据统计信息"""
        if not self.node_counts:
            return {}
        
        return {
            'num_lattices': len(self.node_counts),
            'node_count_range': (min(self.node_counts.values()), max(self.node_counts.values())),
            'edge_count_range': (min(self.edge_counts.values()), max(self.edge_counts.values())),
            'avg_nodes': np.mean(list(self.node_counts.values())),
            'avg_edges': np.mean(list(self.edge_counts.values())),
            'max_nodes_used': self.max_nodes
        }
