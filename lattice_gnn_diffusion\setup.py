from setuptools import setup, find_packages

with open("requirements.txt", "r", encoding="utf-8") as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="lattice-gnn-diffusion",
    version="0.1.0",
    description="Graph Neural Network with Diffusion Models for Lattice Structure Prediction and Generation",
    long_description=open("../README.md", "r", encoding="utf-8").read() if os.path.exists("../README.md") else "",
    long_description_content_type="text/markdown",
    author="AI Assistant",
    author_email="<EMAIL>",
    url="https://github.com/example/lattice-gnn-diffusion",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Physics",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.2.0",
            "black>=21.0.0",
            "flake8>=3.9.0",
            "jupyter>=1.0.0",
        ],
        "viz": [
            "streamlit>=1.10.0",
            "gradio>=3.0.0",
        ],
        "experiment": [
            "tensorboard>=2.8.0",
            "wandb>=0.12.0",
            "optuna>=2.10.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "lattice-train=lattice_gnn_diffusion.training.train:main",
            "lattice-predict=lattice_gnn_diffusion.inference.predict:main",
            "lattice-generate=lattice_gnn_diffusion.inference.generate:main",
        ],
    },
    include_package_data=True,
    package_data={
        "lattice_gnn_diffusion": ["configs/*.yaml"],
    },
)
