# Core Deep Learning Framework
torch>=2.0.0
torch-geometric>=2.3.0
torch-scatter>=2.1.0
torch-sparse>=0.6.0

# Scientific Computing
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Machine Learning
scikit-learn>=1.0.0

# Configuration Management
pyyaml>=6.0
hydra-core>=1.1.0

# Progress Tracking
tqdm>=4.62.0

# Jupyter Support
jupyter>=1.0.0
ipywidgets>=7.6.0

# Development Tools
pytest>=6.2.0
black>=21.0.0
flake8>=3.9.0

# Optional: Web Interface
streamlit>=1.10.0
gradio>=3.0.0

# Optional: Experiment Tracking
tensorboard>=2.8.0
wandb>=0.12.0

# Optional: Advanced Optimization
optuna>=2.10.0
