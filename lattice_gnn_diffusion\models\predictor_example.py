"""
性能预测模块使用示例和集成指南
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import pandas as pd

# 尝试导入依赖
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch_geometric.data import DataLoader
    
    from models.predictor import PerformancePredictor, AdaptivePerformancePredictor
    from models.graph_encoder import GATEncoder
    from training.losses import PerformanceLossManager
    from training.metrics import PerformanceMetrics, MetricsVisualizer
    from data.dataset import LatticeDataModule
    from data.preprocessing import LatticeDataPreprocessor
    
    TORCH_AVAILABLE = True
except ImportError as e:
    print(f"依赖导入失败: {e}")
    TORCH_AVAILABLE = False

def example_1_basic_usage():
    """示例1: 基本使用方法"""
    if not TORCH_AVAILABLE:
        print("跳过示例1（依赖不可用）")
        return
    
    print("=== 示例1: 基本使用方法 ===")
    
    # 1. 创建图编码器
    encoder = GATEncoder(
        input_dim=3,           # 节点坐标维度
        hidden_dim=64,         # 隐藏层维度
        output_dim=128,        # 输出嵌入维度
        num_layers=3,          # GAT层数
        num_heads=4,           # 注意力头数
        dropout=0.1,
        pooling_method='attention'
    )
    
    # 2. 创建性能预测器
    predictor = PerformancePredictor(
        graph_encoder=encoder,
        num_performance_features=18,  # 根据lattice.csv确定
        predictor_config={
            'hidden_dims': [256, 128, 64],
            'activation': 'relu',
            'dropout': 0.1,
            'use_batch_norm': True
        },
        freeze_encoder=False  # 允许端到端训练
    )
    
    print(f"预测器总参数数量: {sum(p.numel() for p in predictor.parameters()):,}")
    
    # 3. 创建虚拟数据进行测试
    batch_size = 4
    num_nodes_per_graph = 16
    
    # 节点特征（坐标）
    x = torch.randn(batch_size * num_nodes_per_graph, 3)
    
    # 边索引（每个图内部连接）
    edge_indices = []
    for i in range(batch_size):
        start_idx = i * num_nodes_per_graph
        # 创建随机边
        num_edges = 20
        edges = torch.randint(start_idx, start_idx + num_nodes_per_graph, (2, num_edges))
        edge_indices.append(edges)
    
    edge_index = torch.cat(edge_indices, dim=1)
    
    # 批索引
    batch = torch.repeat_interleave(torch.arange(batch_size), num_nodes_per_graph)
    
    # 节点mask（模拟变长图）
    node_mask = torch.ones(batch_size * num_nodes_per_graph, dtype=torch.bool)
    
    # 4. 前向传播
    predictions = predictor(x, edge_index, batch, node_mask)
    print(f"预测输出形状: {predictions.shape}")
    
    # 5. 不确定性预测
    mean_pred, std_pred = predictor.predict_with_uncertainty(
        x, edge_index, batch, node_mask, num_samples=10
    )
    print(f"不确定性预测 - 均值: {mean_pred.shape}, 标准差: {std_pred.shape}")
    
    print("✓ 基本使用示例完成\n")

def example_2_adaptive_dimensions():
    """示例2: 自适应维度调整"""
    if not TORCH_AVAILABLE:
        print("跳过示例2（依赖不可用）")
        return
    
    print("=== 示例2: 自适应维度调整 ===")
    
    # 1. 创建自适应预测器
    encoder = GATEncoder(input_dim=3, hidden_dim=64, output_dim=128, num_layers=2)
    
    predictor = AdaptivePerformancePredictor(
        graph_encoder=encoder,
        initial_num_features=18  # 初始特征数量
    )
    
    # 2. 模拟不同的性能特征配置
    configs = [
        {
            'name': '完整性能指标',
            'features': ['mech_Ex', 'mech_Ey', 'mech_Ez', 'mech_Gyz', 'mech_Gxz', 'mech_Gxy',
                        'mech_nuyz', 'mech_nuxz', 'mech_nuxy', 'mech_density',
                        'scaling_x', 'scaling_y', 'scaling_z', 'scaling_xy', 'scaling_xz', 'scaling_yz',
                        'scaling_volume', 'scaling_surface_area']
        },
        {
            'name': '仅弹性模量',
            'features': ['mech_Ex', 'mech_Ey', 'mech_Ez']
        },
        {
            'name': '弹性模量和剪切模量',
            'features': ['mech_Ex', 'mech_Ey', 'mech_Ez', 'mech_Gyz', 'mech_Gxz', 'mech_Gxy']
        }
    ]
    
    # 创建测试数据
    x = torch.randn(32, 3)
    edge_index = torch.randint(0, 32, (2, 64))
    batch = torch.zeros(32, dtype=torch.long)
    node_mask = torch.ones(32, dtype=torch.bool)
    
    for config in configs:
        print(f"\n配置: {config['name']}")
        print(f"特征数量: {len(config['features'])}")
        
        # 更新预测器维度
        predictor.update_output_dimension(
            new_num_features=len(config['features']),
            feature_names=config['features']
        )
        
        # 测试预测
        predictions = predictor(x, edge_index, batch, node_mask)
        print(f"预测输出形状: {predictions.shape}")
        
        # 获取特征信息
        feature_names = predictor.get_feature_names()
        print(f"当前特征: {feature_names[:3]}...")  # 显示前3个
    
    print("✓ 自适应维度示例完成\n")

def example_3_training_pipeline():
    """示例3: 训练流程"""
    if not TORCH_AVAILABLE:
        print("跳过示例3（依赖不可用）")
        return
    
    print("=== 示例3: 训练流程 ===")
    
    # 1. 创建模型
    encoder = GATEncoder(input_dim=3, hidden_dim=64, output_dim=128, num_layers=2)
    predictor = PerformancePredictor(
        graph_encoder=encoder,
        num_performance_features=6  # 简化为6个特征
    )
    
    # 2. 创建损失管理器
    feature_names = ['mech_Ex', 'mech_Ey', 'mech_Ez', 'mech_Gyz', 'mech_Gxz', 'mech_Gxy']
    loss_manager = PerformanceLossManager(
        feature_names=feature_names,
        loss_config={
            'use_adaptive_weights': True,
            'base_loss': 'mse',
            'weight_regularization': 0.01
        }
    )
    
    # 3. 创建评估器
    metrics_calculator = PerformanceMetrics(feature_names)
    
    # 4. 创建优化器
    optimizer = optim.Adam(predictor.parameters(), lr=0.001)
    
    # 5. 模拟训练数据
    def create_batch():
        batch_size = 8
        x = torch.randn(batch_size * 16, 3)
        edge_index = torch.randint(0, batch_size * 16, (2, batch_size * 32))
        batch = torch.repeat_interleave(torch.arange(batch_size), 16)
        node_mask = torch.ones(batch_size * 16, dtype=torch.bool)
        targets = torch.randn(batch_size, 6)
        return x, edge_index, batch, node_mask, targets
    
    # 6. 训练循环
    predictor.train()
    
    print("开始训练...")
    for epoch in range(5):  # 简化为5个epoch
        epoch_loss = 0.0
        num_batches = 3
        
        metrics_calculator.reset()
        
        for batch_idx in range(num_batches):
            # 获取数据
            x, edge_index, batch, node_mask, targets = create_batch()
            
            # 前向传播
            predictions = predictor(x, edge_index, batch, node_mask)
            
            # 计算损失
            loss, loss_details = loss_manager.compute_loss(predictions, targets)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # 更新指标
            metrics_calculator.update(predictions, targets, loss)
            epoch_loss += loss.item()
        
        # 计算epoch指标
        metrics = metrics_calculator.compute_metrics()
        avg_loss = epoch_loss / num_batches
        
        print(f"Epoch {epoch + 1}: Loss={avg_loss:.6f}, R²={metrics.get('r2_overall', 0):.4f}")
        
        # 显示特征权重（如果可用）
        weights = loss_manager.get_feature_weights()
        if weights is not None:
            print(f"  特征权重: {weights.detach().numpy()}")
    
    print("✓ 训练流程示例完成\n")

def example_4_data_integration():
    """示例4: 数据集成"""
    print("=== 示例4: 数据集成 ===")
    
    # 模拟从lattice.csv提取性能特征的过程
    print("1. 模拟lattice.csv数据加载:")
    
    # 创建模拟的lattice数据
    lattice_data = {
        'lattice_name': [f'lattice_{i:02d}' for i in range(5)],
        'mech_Ex': np.random.uniform(0.1, 2.0, 5),
        'mech_Ey': np.random.uniform(0.1, 2.0, 5),
        'mech_Ez': np.random.uniform(0.1, 2.0, 5),
        'mech_Gyz': np.random.uniform(0.05, 1.0, 5),
        'mech_Gxz': np.random.uniform(0.05, 1.0, 5),
        'mech_Gxy': np.random.uniform(0.05, 1.0, 5),
        'scaling_x': np.random.uniform(0.8, 1.2, 5),
        'scaling_y': np.random.uniform(0.8, 1.2, 5),
        'scaling_z': np.random.uniform(0.8, 1.2, 5)
    }
    
    lattice_df = pd.DataFrame(lattice_data)
    print(f"数据形状: {lattice_df.shape}")
    print(f"列名: {list(lattice_df.columns)}")
    
    # 2. 提取性能特征
    performance_cols = [col for col in lattice_df.columns 
                       if col.startswith(('mech_', 'scaling_'))]
    
    print(f"\n2. 提取的性能特征 ({len(performance_cols)}个):")
    for i, col in enumerate(performance_cols, 1):
        print(f"  {i:2d}. {col}")
    
    # 3. 动态创建预测器
    if TORCH_AVAILABLE:
        print(f"\n3. 创建动态预测器:")
        
        encoder = GATEncoder(input_dim=3, hidden_dim=64, output_dim=128, num_layers=2)
        predictor = AdaptivePerformancePredictor(
            graph_encoder=encoder,
            initial_num_features=len(performance_cols)
        )
        
        # 更新特征名称
        predictor.update_output_dimension(
            new_num_features=len(performance_cols),
            feature_names=performance_cols
        )
        
        print(f"预测器输出维度: {predictor.num_performance_features}")
        print(f"特征名称: {predictor.get_feature_names()}")
        
        # 4. 模拟不同用户输入
        print(f"\n4. 模拟用户选择不同性能参数:")
        
        user_selections = [
            ['mech_Ex', 'mech_Ey', 'mech_Ez'],  # 仅弹性模量
            ['mech_Ex', 'mech_Ey', 'mech_Ez', 'mech_Gyz', 'mech_Gxz', 'mech_Gxy'],  # 弹性+剪切
            performance_cols  # 全部特征
        ]
        
        for i, selection in enumerate(user_selections, 1):
            print(f"\n  用户选择 {i}: {len(selection)} 个特征")
            
            # 更新预测器
            predictor.update_output_dimension(
                new_num_features=len(selection),
                feature_names=selection
            )
            
            # 测试预测
            x = torch.randn(16, 3)
            edge_index = torch.randint(0, 16, (2, 32))
            batch = torch.zeros(16, dtype=torch.long)
            node_mask = torch.ones(16, dtype=torch.bool)
            
            pred = predictor(x, edge_index, batch, node_mask)
            print(f"    预测输出: {pred.shape}")
    
    print("✓ 数据集成示例完成\n")

def example_5_evaluation_and_visualization():
    """示例5: 评估和可视化"""
    if not TORCH_AVAILABLE:
        print("跳过示例5（依赖不可用）")
        return
    
    print("=== 示例5: 评估和可视化 ===")
    
    # 1. 创建测试数据
    batch_size, num_features = 50, 6
    feature_names = ['mech_Ex', 'mech_Ey', 'mech_Ez', 'mech_Gyz', 'mech_Gxz', 'mech_Gxy']
    
    # 创建有相关性的预测和目标数据
    target = torch.randn(batch_size, num_features)
    noise = torch.randn(batch_size, num_features) * 0.3
    pred = target + noise  # 添加噪声模拟预测误差
    
    # 2. 计算评估指标
    metrics_calculator = PerformanceMetrics(feature_names)
    metrics_calculator.update(pred, target)
    
    metrics = metrics_calculator.compute_metrics()
    
    print("评估指标结果:")
    print(f"  整体R²: {metrics.get('r2_overall', 0):.4f}")
    print(f"  整体RMSE: {metrics.get('rmse_overall', 0):.4f}")
    print(f"  平均R²: {metrics.get('r2_mean', 0):.4f}")
    print(f"  R²标准差: {metrics.get('r2_std', 0):.4f}")
    
    # 3. 分特征结果
    print(f"\n分特征R²:")
    for i, name in enumerate(feature_names):
        r2_key = f'r2_{name}'
        if r2_key in metrics:
            print(f"  {name}: {metrics[r2_key]:.4f}")
    
    # 4. 最差和最好的预测
    worst = metrics_calculator.get_worst_predictions(top_k=3)
    best = metrics_calculator.get_best_predictions(top_k=3)
    
    print(f"\n最差预测样本 (前3个):")
    for i, (idx, error) in enumerate(zip(worst['indices'], worst['errors'])):
        print(f"  样本 {idx}: MSE = {error:.6f}")
    
    print(f"\n最好预测样本 (前3个):")
    for i, (idx, error) in enumerate(zip(best['indices'], best['errors'])):
        print(f"  样本 {idx}: MSE = {error:.6f}")
    
    # 5. 可视化（如果matplotlib可用）
    try:
        import matplotlib.pyplot as plt
        
        visualizer = MetricsVisualizer(feature_names)
        
        # 创建散点图
        fig1 = visualizer.plot_prediction_scatter(
            pred.numpy(), target.numpy(),
            feature_indices=[0, 1, 2]  # 显示前3个特征
        )
        print(f"\n✓ 创建了预测散点图")
        plt.close(fig1)
        
        # 创建特征重要性图
        fig2 = visualizer.plot_feature_importance(metrics, metric_type='r2')
        print(f"✓ 创建了特征重要性图")
        plt.close(fig2)
        
        # 创建误差分布图
        fig3 = visualizer.plot_error_distribution(pred.numpy(), target.numpy())
        print(f"✓ 创建了误差分布图")
        plt.close(fig3)
        
    except ImportError:
        print("matplotlib不可用，跳过可视化")
    
    print("✓ 评估和可视化示例完成\n")

def main():
    """主函数"""
    print("性能预测模块使用示例\n")
    
    examples = [
        example_1_basic_usage,
        example_2_adaptive_dimensions,
        example_3_training_pipeline,
        example_4_data_integration,
        example_5_evaluation_and_visualization
    ]
    
    for example in examples:
        try:
            example()
        except Exception as e:
            print(f"示例执行失败: {e}\n")
    
    print("=== 总结 ===")
    print("性能预测模块提供了以下核心功能:")
    print("1. 动态维度调整 - 根据用户选择的性能参数自动调整")
    print("2. 多种损失函数 - 支持加权、自适应、多任务损失")
    print("3. 完整评估体系 - R²、RMSE、MAE、MAPE等指标")
    print("4. 不确定性量化 - 基于Dropout的预测不确定性")
    print("5. 可视化分析 - 散点图、特征重要性、误差分布")
    print("6. 权重迁移 - 维度变化时的智能权重迁移")
    
    print("\n使用建议:")
    print("• 根据lattice.csv中的性能列动态创建预测器")
    print("• 使用自适应权重损失处理不同尺度的性能指标")
    print("• 定期评估分特征性能，识别预测困难的指标")
    print("• 利用不确定性预测评估模型置信度")
    print("• 使用可视化工具分析预测质量和误差模式")

if __name__ == "__main__":
    main()
