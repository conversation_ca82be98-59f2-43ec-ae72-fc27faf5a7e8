"""
测试GAT编码器代码结构和语法正确性（无需PyTorch）
"""

import sys
import os
import ast
import importlib.util

def check_python_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 解析AST
        ast.parse(source)
        return True, "语法正确"
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"其他错误: {e}"

def analyze_class_structure(file_path):
    """分析类结构"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        tree = ast.parse(source)
        
        classes = []
        functions = []
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                methods = [n.name for n in node.body if isinstance(n, ast.FunctionDef)]
                classes.append({
                    'name': node.name,
                    'methods': methods,
                    'line': node.lineno
                })
            elif isinstance(node, ast.FunctionDef) and not any(isinstance(parent, ast.ClassDef) for parent in ast.walk(tree)):
                functions.append({
                    'name': node.name,
                    'line': node.lineno
                })
            elif isinstance(node, (ast.Import, ast.ImportFrom)):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                else:
                    module = node.module or ''
                    for alias in node.names:
                        imports.append(f"{module}.{alias.name}")
        
        return {
            'classes': classes,
            'functions': functions,
            'imports': imports
        }
    except Exception as e:
        return {'error': str(e)}

def test_layers_py():
    """测试layers.py"""
    print("=== 测试 layers.py ===")
    
    file_path = "layers.py"
    
    # 检查语法
    syntax_ok, syntax_msg = check_python_syntax(file_path)
    print(f"语法检查: {'✓' if syntax_ok else '✗'} {syntax_msg}")
    
    if not syntax_ok:
        return False
    
    # 分析结构
    structure = analyze_class_structure(file_path)
    
    if 'error' in structure:
        print(f"✗ 结构分析失败: {structure['error']}")
        return False
    
    print(f"类数量: {len(structure['classes'])}")
    print(f"函数数量: {len(structure['functions'])}")
    print(f"导入数量: {len(structure['imports'])}")
    
    # 检查关键类
    expected_classes = ['MultiHeadGATLayer', 'GraphNorm', 'ResidualBlock', 'PositionalEncoding']
    found_classes = [cls['name'] for cls in structure['classes']]
    
    for expected in expected_classes:
        if expected in found_classes:
            print(f"✓ 找到类: {expected}")
        else:
            print(f"✗ 缺少类: {expected}")
            return False
    
    # 检查MultiHeadGATLayer的方法
    gat_class = next((cls for cls in structure['classes'] if cls['name'] == 'MultiHeadGATLayer'), None)
    if gat_class:
        expected_methods = ['__init__', 'reset_parameters', 'forward', 'message']
        for method in expected_methods:
            if method in gat_class['methods']:
                print(f"✓ MultiHeadGATLayer.{method}")
            else:
                print(f"✗ 缺少方法: MultiHeadGATLayer.{method}")
                return False
    
    print("✓ layers.py 结构检查通过")
    return True

def test_graph_encoder_py():
    """测试graph_encoder.py"""
    print("\n=== 测试 graph_encoder.py ===")
    
    file_path = "graph_encoder.py"
    
    # 检查语法
    syntax_ok, syntax_msg = check_python_syntax(file_path)
    print(f"语法检查: {'✓' if syntax_ok else '✗'} {syntax_msg}")
    
    if not syntax_ok:
        return False
    
    # 分析结构
    structure = analyze_class_structure(file_path)
    
    if 'error' in structure:
        print(f"✗ 结构分析失败: {structure['error']}")
        return False
    
    print(f"类数量: {len(structure['classes'])}")
    print(f"函数数量: {len(structure['functions'])}")
    
    # 检查关键类
    expected_classes = ['AttentionPooling', 'GATEncoder', 'MultiScaleGATEncoder']
    found_classes = [cls['name'] for cls in structure['classes']]
    
    for expected in expected_classes:
        if expected in found_classes:
            print(f"✓ 找到类: {expected}")
        else:
            print(f"✗ 缺少类: {expected}")
            return False
    
    # 检查GATEncoder的方法
    encoder_class = next((cls for cls in structure['classes'] if cls['name'] == 'GATEncoder'), None)
    if encoder_class:
        expected_methods = ['__init__', 'reset_parameters', 'forward', 'get_attention_weights']
        for method in expected_methods:
            if method in encoder_class['methods']:
                print(f"✓ GATEncoder.{method}")
            else:
                print(f"✗ 缺少方法: GATEncoder.{method}")
                return False
    
    print("✓ graph_encoder.py 结构检查通过")
    return True

def test_graph_utils_py():
    """测试graph_utils.py"""
    print("\n=== 测试 ../utils/graph_utils.py ===")
    
    file_path = "../utils/graph_utils.py"
    
    # 检查语法
    syntax_ok, syntax_msg = check_python_syntax(file_path)
    print(f"语法检查: {'✓' if syntax_ok else '✗'} {syntax_msg}")
    
    if not syntax_ok:
        return False
    
    # 分析结构
    structure = analyze_class_structure(file_path)
    
    if 'error' in structure:
        print(f"✗ 结构分析失败: {structure['error']}")
        return False
    
    print(f"函数数量: {len(structure['functions'])}")
    
    # 检查关键函数
    expected_functions = [
        'create_batch_from_data_list',
        'extract_subgraph', 
        'compute_graph_statistics',
        'visualize_graph_3d',
        'compute_node_centrality',
        'add_edge_features',
        'normalize_graph_coordinates',
        'check_graph_connectivity',
        'compute_graph_laplacian'
    ]
    
    found_functions = [func['name'] for func in structure['functions']]
    
    for expected in expected_functions:
        if expected in found_functions:
            print(f"✓ 找到函数: {expected}")
        else:
            print(f"✗ 缺少函数: {expected}")
            return False
    
    print("✓ graph_utils.py 结构检查通过")
    return True

def check_imports():
    """检查导入依赖"""
    print("\n=== 检查导入依赖 ===")
    
    files_to_check = [
        ("layers.py", ["torch", "torch.nn", "torch.nn.functional", "torch_geometric.nn"]),
        ("graph_encoder.py", ["torch", "torch.nn", "torch_geometric.nn", ".layers"]),
        ("../utils/graph_utils.py", ["torch", "numpy", "torch_geometric.data", "networkx"])
    ]
    
    all_good = True
    
    for file_path, expected_imports in files_to_check:
        print(f"\n检查 {file_path}:")
        
        structure = analyze_class_structure(file_path)
        if 'error' in structure:
            print(f"✗ 无法分析: {structure['error']}")
            all_good = False
            continue
        
        imports = structure['imports']
        
        for expected in expected_imports:
            # 简化检查：只要包含关键词即可
            found = any(expected.replace('.', '') in imp.replace('.', '') for imp in imports)
            if found:
                print(f"✓ 导入: {expected}")
            else:
                print(f"? 可能缺少: {expected}")
    
    return all_good

def check_file_sizes():
    """检查文件大小"""
    print("\n=== 检查文件大小 ===")
    
    files = [
        "layers.py",
        "graph_encoder.py", 
        "../utils/graph_utils.py",
        "test_encoder.py"
    ]
    
    for file_path in files:
        try:
            size = os.path.getsize(file_path)
            lines = 0
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
            
            print(f"{file_path}: {size:,} 字节, {lines} 行")
            
            if size == 0:
                print(f"✗ {file_path} 文件为空")
                return False
            elif lines < 10:
                print(f"? {file_path} 文件过小")
        
        except FileNotFoundError:
            print(f"✗ 文件不存在: {file_path}")
            return False
        except Exception as e:
            print(f"✗ 检查 {file_path} 时出错: {e}")
            return False
    
    print("✓ 文件大小检查通过")
    return True

def main():
    """主测试函数"""
    print("开始GAT编码器结构测试...\n")
    
    tests = [
        check_file_sizes,
        test_layers_py,
        test_graph_encoder_py,
        test_graph_utils_py,
        check_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test_func.__name__} 失败: {e}")
    
    print(f"\n=== 结构测试总结 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有结构测试通过！代码结构正确。")
        print("\n📋 实现总结:")
        print("✓ MultiHeadGATLayer - 多头图注意力层")
        print("✓ GraphNorm - 图归一化层") 
        print("✓ ResidualBlock - 残差连接块")
        print("✓ PositionalEncoding - 位置编码")
        print("✓ AttentionPooling - 注意力池化")
        print("✓ GATEncoder - 主要GAT编码器")
        print("✓ MultiScaleGATEncoder - 多尺度编码器")
        print("✓ 图工具函数集合")
        
        print("\n🔧 核心特性:")
        print("• 支持变长图处理（mask机制）")
        print("• 多头注意力机制")
        print("• 残差连接和层归一化")
        print("• 多种池化策略")
        print("• 位置编码支持")
        print("• 批处理支持")
        print("• 梯度流优化")
        
    else:
        print(f"✗ {total - passed} 个测试失败")
    
    print("\n📦 需要的依赖包:")
    print("pip install torch torch-geometric numpy matplotlib networkx")

if __name__ == "__main__":
    main()
