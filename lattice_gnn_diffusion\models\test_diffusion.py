"""
扩散模型测试：验证条件扩散模型的功能
"""

import torch
import torch.nn as nn
import numpy as np
import pytest
from typing import Dict, List, Tuple, Optional

# 导入扩散模型组件
try:
    from .diffusion_core import (
        NoiseScheduler, ConditionalUNet, ConditionalDDPM, 
        GraphDiffusionModel, EdgePredictor
    )
    from .conditioning import (
        PerformanceConditioner, AdaptiveConditioner,
        ConditionalCrossAttention, MultiScaleConditioner
    )
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from diffusion_core import (
        NoiseScheduler, ConditionalUNet, ConditionalDDPM, 
        GraphDiffusionModel, EdgePredictor
    )
    from conditioning import (
        PerformanceConditioner, AdaptiveConditioner,
        ConditionalCrossAttention, MultiScaleConditioner
    )

def test_noise_scheduler():
    """测试噪声调度器"""
    print("测试噪声调度器...")
    
    # 测试不同调度类型
    for schedule_type in ['linear', 'cosine', 'quadratic']:
        scheduler = NoiseScheduler(
            num_timesteps=100,
            schedule_type=schedule_type
        )
        
        # 检查基本属性
        assert scheduler.betas.shape[0] == 100
        assert torch.all(scheduler.betas > 0)
        assert torch.all(scheduler.betas < 1)
        assert torch.all(scheduler.alphas_cumprod <= 1)
        assert torch.all(scheduler.alphas_cumprod > 0)
        
        print(f"  ✓ {schedule_type} 调度器通过测试")
    
    # 测试前向扩散
    scheduler = NoiseScheduler(num_timesteps=10)
    batch_size, dim = 4, 16
    x_start = torch.randn(batch_size, dim)
    t = torch.randint(0, 10, (batch_size,))
    
    x_t = scheduler.q_sample(x_start, t)
    assert x_t.shape == x_start.shape
    
    # 测试预测原始数据
    noise = torch.randn_like(x_start)
    x_pred = scheduler.predict_start_from_noise(x_t, t, noise)
    assert x_pred.shape == x_start.shape
    
    print("  ✓ 前向扩散和预测功能正常")

def test_conditional_unet():
    """测试条件U-Net"""
    print("测试条件U-Net...")
    
    input_dim = 64
    condition_dim = 18
    batch_size = 4
    
    unet = ConditionalUNet(
        input_dim=input_dim,
        condition_dim=condition_dim,
        hidden_dims=[32, 64, 32],
        time_embed_dim=32
    )
    
    # 测试前向传播
    x = torch.randn(batch_size, input_dim)
    t = torch.randint(0, 1000, (batch_size,))
    condition = torch.randn(batch_size, condition_dim)
    
    output = unet(x, t, condition)
    
    assert output.shape == (batch_size, input_dim)
    print("  ✓ 条件U-Net前向传播正常")
    
    # 测试梯度
    loss = output.sum()
    loss.backward()
    
    # 检查是否有梯度
    has_grad = any(p.grad is not None for p in unet.parameters())
    assert has_grad, "模型参数没有梯度"
    
    print("  ✓ 梯度计算正常")

def test_conditional_ddpm():
    """测试条件DDPM"""
    print("测试条件DDPM...")
    
    input_dim = 32
    condition_dim = 18
    batch_size = 4
    
    ddpm = ConditionalDDPM(
        input_dim=input_dim,
        condition_dim=condition_dim,
        num_timesteps=100,
        unet_config={'hidden_dims': [64, 128, 64]}
    )
    
    # 测试训练前向传播
    x = torch.randn(batch_size, input_dim)
    condition = torch.randn(batch_size, condition_dim)
    
    loss_info = ddpm(x, condition)
    
    assert 'loss' in loss_info
    assert 'predicted_noise' in loss_info
    assert 'target_noise' in loss_info
    assert loss_info['predicted_noise'].shape == (batch_size, input_dim)
    
    print("  ✓ 训练前向传播正常")
    
    # 测试采样
    with torch.no_grad():
        samples = ddpm.sample(condition[:2])  # 采样前两个条件
        assert samples.shape == (2, input_dim)
        
        # 测试DDIM采样
        ddim_samples = ddpm.ddim_sample(condition[:2], num_inference_steps=10)
        assert ddim_samples.shape == (2, input_dim)
    
    print("  ✓ 采样功能正常")

def test_edge_predictor():
    """测试边预测器"""
    print("测试边预测器...")
    
    max_nodes = 16
    node_dim = 3
    condition_dim = 18
    batch_size = 4
    
    edge_predictor = EdgePredictor(
        node_dim=node_dim,
        condition_dim=condition_dim,
        max_nodes=max_nodes,
        hidden_dims=[64, 128, 64]
    )
    
    # 创建测试数据
    node_coords = torch.randn(batch_size, max_nodes, node_dim)
    condition = torch.randn(batch_size, condition_dim)
    node_mask = torch.ones(batch_size, max_nodes, dtype=torch.bool)
    
    # 设置不同的节点数
    for i in range(batch_size):
        num_nodes = 8 + i * 2  # 8, 10, 12, 14
        node_mask[i, num_nodes:] = False
    
    # 创建边索引
    edge_index = torch.zeros(batch_size, 2, 10, dtype=torch.long)
    for i in range(batch_size):
        num_nodes = node_mask[i].sum().item()
        # 创建一些随机边
        edges = torch.randint(0, num_nodes, (2, min(5, num_nodes-1)))
        edge_index[i, :, :edges.shape[1]] = edges
    
    # 测试前向传播
    loss_info = edge_predictor(node_coords, edge_index, condition, node_mask)
    
    assert 'loss' in loss_info
    assert 'edge_probs' in loss_info
    assert loss_info['edge_probs'].shape == (batch_size, max_nodes, max_nodes)
    
    print("  ✓ 边预测器前向传播正常")
    
    # 测试边预测
    with torch.no_grad():
        edge_probs = edge_predictor.predict_edges(node_coords, condition, node_mask)
        assert edge_probs.shape == (batch_size, max_nodes, max_nodes)
        
        # 测试边采样
        sampled_edges = edge_predictor.sample_edges(edge_probs, node_mask)
        assert sampled_edges.shape[0] == batch_size
        assert sampled_edges.shape[1] == 2
    
    print("  ✓ 边预测和采样功能正常")

def test_graph_diffusion_model():
    """测试图扩散模型"""
    print("测试图扩散模型...")
    
    max_nodes = 16
    node_dim = 3
    condition_dim = 18
    batch_size = 2  # 减少批次大小以节省内存
    
    graph_diffusion = GraphDiffusionModel(
        max_nodes=max_nodes,
        node_dim=node_dim,
        condition_dim=condition_dim,
        num_timesteps=50,  # 减少时间步以加快测试
        coordinate_diffusion_config={
            'unet_config': {'hidden_dims': [64, 128, 64]}
        },
        edge_prediction_config={
            'hidden_dims': [64, 128, 64]
        }
    )
    
    # 创建测试数据
    node_coords = torch.randn(batch_size, max_nodes, node_dim)
    condition = torch.randn(batch_size, condition_dim)
    node_mask = torch.ones(batch_size, max_nodes, dtype=torch.bool)
    
    # 设置节点数
    node_mask[0, 12:] = False  # 第一个图12个节点
    node_mask[1, 10:] = False  # 第二个图10个节点
    
    # 创建边索引
    edge_index = torch.zeros(batch_size, 2, 8, dtype=torch.long)
    
    # 测试训练前向传播
    loss_info = graph_diffusion(node_coords, edge_index, condition, node_mask)
    
    assert 'total_loss' in loss_info
    assert 'coordinate_loss' in loss_info
    assert 'edge_loss' in loss_info
    
    print("  ✓ 图扩散模型训练前向传播正常")
    
    # 测试生成
    with torch.no_grad():
        generated = graph_diffusion.generate(
            condition=condition,
            num_nodes=torch.tensor([12, 10]),
            use_ddim=True,
            ddim_steps=10
        )
        
        assert 'node_coords' in generated
        assert 'edge_index' in generated
        assert 'node_mask' in generated
        assert generated['node_coords'].shape == (batch_size, max_nodes, node_dim)
    
    print("  ✓ 图生成功能正常")

def test_performance_conditioner():
    """测试性能条件编码器"""
    print("测试性能条件编码器...")
    
    performance_dim = 18
    condition_dim = 128
    batch_size = 4
    
    # 测试MLP编码器
    mlp_conditioner = PerformanceConditioner(
        performance_dim=performance_dim,
        condition_dim=condition_dim,
        encoding_type='mlp'
    )
    
    performance = torch.randn(batch_size, performance_dim)
    condition = mlp_conditioner(performance)
    
    assert condition.shape == (batch_size, condition_dim)
    print("  ✓ MLP条件编码器正常")
    
    # 测试嵌入编码器
    embed_conditioner = PerformanceConditioner(
        performance_dim=performance_dim,
        condition_dim=condition_dim,
        encoding_type='embedding'
    )
    
    condition = embed_conditioner(performance)
    assert condition.shape == (batch_size, condition_dim)
    print("  ✓ 嵌入条件编码器正常")

def test_adaptive_conditioner():
    """测试自适应条件编码器"""
    print("测试自适应条件编码器...")
    
    max_dim = 30
    condition_dim = 128
    batch_size = 4
    
    adaptive_conditioner = AdaptiveConditioner(
        max_performance_dim=max_dim,
        condition_dim=condition_dim
    )
    
    # 测试不同维度
    for dim in [10, 18, 25, 30]:
        performance = torch.randn(batch_size, dim)
        condition = adaptive_conditioner(performance)
        assert condition.shape == (batch_size, condition_dim)
    
    print("  ✓ 自适应条件编码器正常")

def test_conditional_cross_attention():
    """测试条件交叉注意力"""
    print("测试条件交叉注意力...")
    
    feature_dim = 128
    condition_dim = 64
    seq_len = 16
    batch_size = 4
    
    cross_attention = ConditionalCrossAttention(
        feature_dim=feature_dim,
        condition_dim=condition_dim,
        num_heads=8
    )
    
    features = torch.randn(batch_size, seq_len, feature_dim)
    condition = torch.randn(batch_size, condition_dim)
    feature_mask = torch.ones(batch_size, seq_len, dtype=torch.bool)
    
    # 设置部分mask
    feature_mask[0, 12:] = False
    feature_mask[1, 10:] = False
    
    output = cross_attention(features, condition, feature_mask)
    
    assert output.shape == (batch_size, seq_len, feature_dim)
    print("  ✓ 条件交叉注意力正常")

def run_all_tests():
    """运行所有测试"""
    print("开始扩散模型测试...")
    print("=" * 50)
    
    try:
        test_noise_scheduler()
        test_conditional_unet()
        test_conditional_ddpm()
        test_edge_predictor()
        test_graph_diffusion_model()
        test_performance_conditioner()
        test_adaptive_conditioner()
        test_conditional_cross_attention()
        
        print("=" * 50)
        print("✅ 所有扩散模型测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    run_all_tests()
