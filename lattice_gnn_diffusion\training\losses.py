"""
损失函数定义：多任务学习损失函数和相关工具
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, List, Dict, Any, Tuple
import numpy as np

class WeightedMSELoss(nn.Module):
    """加权均方误差损失"""
    
    def __init__(self, weights: Optional[torch.Tensor] = None, 
                 reduction: str = 'mean'):
        """
        初始化加权MSE损失
        
        Args:
            weights: 各特征的权重 [num_features]
            reduction: 减少方式 ('mean', 'sum', 'none')
        """
        super().__init__()
        self.register_buffer('weights', weights)
        self.reduction = reduction
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            pred: 预测值 [batch_size, num_features]
            target: 目标值 [batch_size, num_features]
            
        Returns:
            损失值
        """
        mse = (pred - target) ** 2
        
        if self.weights is not None:
            mse = mse * self.weights.unsqueeze(0)
        
        if self.reduction == 'mean':
            return mse.mean()
        elif self.reduction == 'sum':
            return mse.sum()
        else:
            return mse

class AdaptiveWeightedLoss(nn.Module):
    """自适应加权损失"""
    
    def __init__(self, num_features: int, init_weights: Optional[torch.Tensor] = None,
                 base_loss: str = 'mse', learn_weights: bool = True,
                 weight_regularization: float = 0.01):
        """
        初始化自适应加权损失
        
        Args:
            num_features: 特征数量
            init_weights: 初始权重
            base_loss: 基础损失类型 ('mse', 'mae', 'huber')
            learn_weights: 是否学习权重
            weight_regularization: 权重正则化系数
        """
        super().__init__()
        
        self.num_features = num_features
        self.base_loss = base_loss
        self.learn_weights = learn_weights
        self.weight_regularization = weight_regularization
        
        # 初始化权重
        if init_weights is not None:
            weights = init_weights.clone()
        else:
            weights = torch.ones(num_features)
        
        if learn_weights:
            self.log_weights = nn.Parameter(torch.log(weights))
        else:
            self.register_buffer('log_weights', torch.log(weights))
        
        # 基础损失函数
        if base_loss == 'mse':
            self.loss_fn = nn.MSELoss(reduction='none')
        elif base_loss == 'mae':
            self.loss_fn = nn.L1Loss(reduction='none')
        elif base_loss == 'huber':
            self.loss_fn = nn.HuberLoss(reduction='none', delta=1.0)
        else:
            raise ValueError(f"Unknown base loss: {base_loss}")
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播
        
        Args:
            pred: 预测值 [batch_size, num_features]
            target: 目标值 [batch_size, num_features]
            
        Returns:
            (总损失, 损失详情字典)
        """
        # 计算基础损失
        base_losses = self.loss_fn(pred, target)  # [batch_size, num_features]
        
        # 获取权重
        weights = torch.exp(self.log_weights)  # [num_features]
        
        # 加权损失
        weighted_losses = base_losses * weights.unsqueeze(0)
        
        # 总损失
        total_loss = weighted_losses.mean()
        
        # 权重正则化
        if self.learn_weights and self.weight_regularization > 0:
            weight_reg = self.weight_regularization * (weights - 1.0).pow(2).mean()
            total_loss = total_loss + weight_reg
        else:
            weight_reg = torch.tensor(0.0, device=total_loss.device)
        
        # 损失详情
        loss_details = {
            'total_loss': total_loss,
            'base_loss': base_losses.mean(),
            'weighted_loss': weighted_losses.mean(),
            'weight_regularization': weight_reg,
            'feature_losses': base_losses.mean(dim=0),  # 各特征平均损失
            'weights': weights
        }
        
        return total_loss, loss_details
    
    def get_weights(self) -> torch.Tensor:
        """获取当前权重"""
        return torch.exp(self.log_weights)
    
    def set_weights(self, weights: torch.Tensor):
        """设置权重"""
        if self.learn_weights:
            self.log_weights.data = torch.log(weights)
        else:
            self.log_weights = torch.log(weights)

class MultiTaskLoss(nn.Module):
    """多任务学习损失"""
    
    def __init__(self, task_configs: List[Dict[str, Any]], 
                 uncertainty_weighting: bool = True):
        """
        初始化多任务损失
        
        Args:
            task_configs: 任务配置列表，每个包含 {'name', 'indices', 'weight', 'loss_type'}
            uncertainty_weighting: 是否使用不确定性加权
        """
        super().__init__()
        
        self.task_configs = task_configs
        self.uncertainty_weighting = uncertainty_weighting
        
        # 创建各任务的损失函数
        self.task_losses = nn.ModuleDict()
        self.task_weights = {}
        
        for config in task_configs:
            name = config['name']
            loss_type = config.get('loss_type', 'mse')
            
            if loss_type == 'mse':
                self.task_losses[name] = nn.MSELoss()
            elif loss_type == 'mae':
                self.task_losses[name] = nn.L1Loss()
            elif loss_type == 'huber':
                self.task_losses[name] = nn.HuberLoss()
            
            self.task_weights[name] = config.get('weight', 1.0)
        
        # 不确定性权重（可学习）
        if uncertainty_weighting:
            self.log_vars = nn.ParameterDict({
                name: nn.Parameter(torch.zeros(1))
                for name in [config['name'] for config in task_configs]
            })
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播
        
        Args:
            pred: 预测值 [batch_size, total_features]
            target: 目标值 [batch_size, total_features]
            
        Returns:
            (总损失, 损失详情字典)
        """
        total_loss = 0.0
        loss_details = {}
        
        for config in self.task_configs:
            name = config['name']
            indices = config['indices']
            
            # 提取任务相关的预测和目标
            task_pred = pred[:, indices]
            task_target = target[:, indices]
            
            # 计算任务损失
            task_loss = self.task_losses[name](task_pred, task_target)
            
            # 应用权重
            if self.uncertainty_weighting:
                # 不确定性加权
                precision = torch.exp(-self.log_vars[name])
                weighted_loss = precision * task_loss + self.log_vars[name]
            else:
                # 固定权重
                weighted_loss = self.task_weights[name] * task_loss
            
            total_loss += weighted_loss
            loss_details[f'{name}_loss'] = task_loss
            loss_details[f'{name}_weighted_loss'] = weighted_loss
            
            if self.uncertainty_weighting:
                loss_details[f'{name}_uncertainty'] = torch.exp(self.log_vars[name])
        
        loss_details['total_loss'] = total_loss
        
        return total_loss, loss_details

class RobustLoss(nn.Module):
    """鲁棒损失函数"""
    
    def __init__(self, loss_type: str = 'huber', delta: float = 1.0,
                 outlier_threshold: float = 3.0):
        """
        初始化鲁棒损失
        
        Args:
            loss_type: 损失类型 ('huber', 'quantile', 'cauchy')
            delta: Huber损失的阈值
            outlier_threshold: 异常值阈值（标准差倍数）
        """
        super().__init__()
        
        self.loss_type = loss_type
        self.delta = delta
        self.outlier_threshold = outlier_threshold
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        residual = pred - target
        
        if self.loss_type == 'huber':
            return F.huber_loss(pred, target, delta=self.delta)
        
        elif self.loss_type == 'quantile':
            # 分位数损失（对异常值不敏感）
            tau = 0.5  # 中位数
            error = target - pred
            loss = torch.max(tau * error, (tau - 1) * error)
            return loss.mean()
        
        elif self.loss_type == 'cauchy':
            # Cauchy损失（对异常值非常鲁棒）
            c = self.delta
            loss = c**2 * torch.log(1 + (residual / c)**2)
            return loss.mean()
        
        else:
            raise ValueError(f"Unknown robust loss type: {self.loss_type}")

class PerformanceLossManager:
    """性能损失管理器"""
    
    def __init__(self, feature_names: List[str], 
                 feature_groups: Optional[Dict[str, List[str]]] = None,
                 loss_config: Optional[Dict[str, Any]] = None):
        """
        初始化损失管理器
        
        Args:
            feature_names: 特征名称列表
            feature_groups: 特征分组 {'group_name': ['feature1', 'feature2']}
            loss_config: 损失配置
        """
        self.feature_names = feature_names
        self.feature_groups = feature_groups or {}
        self.num_features = len(feature_names)
        
        # 默认配置
        default_config = {
            'base_loss': 'mse',
            'use_adaptive_weights': True,
            'use_robust_loss': False,
            'weight_regularization': 0.01,
            'robust_delta': 1.0
        }
        
        self.config = default_config
        if loss_config:
            self.config.update(loss_config)
        
        # 创建损失函数
        self.loss_fn = self._create_loss_function()
    
    def _create_loss_function(self) -> nn.Module:
        """创建损失函数"""
        if self.config['use_robust_loss']:
            return RobustLoss(
                loss_type='huber',
                delta=self.config['robust_delta']
            )
        elif self.config['use_adaptive_weights']:
            return AdaptiveWeightedLoss(
                num_features=self.num_features,
                base_loss=self.config['base_loss'],
                weight_regularization=self.config['weight_regularization']
            )
        else:
            if self.config['base_loss'] == 'mse':
                return nn.MSELoss()
            elif self.config['base_loss'] == 'mae':
                return nn.L1Loss()
            elif self.config['base_loss'] == 'huber':
                return nn.HuberLoss()
    
    def compute_loss(self, pred: torch.Tensor, target: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """计算损失"""
        if isinstance(self.loss_fn, (AdaptiveWeightedLoss, MultiTaskLoss)):
            return self.loss_fn(pred, target)
        else:
            loss = self.loss_fn(pred, target)
            return loss, {'total_loss': loss}
    
    def get_feature_weights(self) -> Optional[torch.Tensor]:
        """获取特征权重"""
        if hasattr(self.loss_fn, 'get_weights'):
            return self.loss_fn.get_weights()
        return None
    
    def update_feature_names(self, new_feature_names: List[str]):
        """更新特征名称"""
        if len(new_feature_names) != self.num_features:
            print(f"特征数量变化: {self.num_features} -> {len(new_feature_names)}")
            self.num_features = len(new_feature_names)
            # 重新创建损失函数
            self.loss_fn = self._create_loss_function()
        
        self.feature_names = new_feature_names
