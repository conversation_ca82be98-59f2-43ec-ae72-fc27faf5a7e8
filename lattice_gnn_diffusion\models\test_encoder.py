"""
测试GAT编码器的功能和性能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
from torch_geometric.data import Data, Batch
import numpy as np
import time

# 尝试导入模型（如果PyTorch可用）
try:
    from models.graph_encoder import GATEncoder, MultiScaleGATEncoder
    from models.layers import MultiHeadGATLayer, GraphNorm, AttentionPooling
    from utils.graph_utils import compute_graph_statistics, create_batch_from_data_list
    TORCH_AVAILABLE = True
except ImportError as e:
    print(f"PyTorch相关模块导入失败: {e}")
    TORCH_AVAILABLE = False

def create_test_graph(num_nodes: int = 20, num_edges: int = 30, 
                     feature_dim: int = 3) -> Data:
    """创建测试图"""
    if not TORCH_AVAILABLE:
        return None
    
    # 随机节点特征
    x = torch.randn(num_nodes, feature_dim)
    
    # 随机边索引
    edge_index = torch.randint(0, num_nodes, (2, num_edges))
    
    # 创建节点mask（模拟变长图）
    valid_nodes = min(num_nodes, np.random.randint(8, num_nodes + 1))
    node_mask = torch.zeros(num_nodes, dtype=torch.bool)
    node_mask[:valid_nodes] = True
    
    # 过滤边索引，确保边连接的都是有效节点
    valid_edge_mask = (edge_index[0] < valid_nodes) & (edge_index[1] < valid_nodes)
    edge_index = edge_index[:, valid_edge_mask]
    
    data = Data(x=x, edge_index=edge_index, node_mask=node_mask, num_nodes=valid_nodes)
    return data

def test_gat_layer():
    """测试GAT层"""
    if not TORCH_AVAILABLE:
        print("跳过GAT层测试（PyTorch不可用）")
        return False
    
    print("=== 测试GAT层 ===")
    
    try:
        # 创建GAT层
        gat_layer = MultiHeadGATLayer(
            in_channels=3,
            out_channels=16,
            heads=4,
            concat=True,
            dropout=0.1
        )
        
        # 创建测试数据
        data = create_test_graph(num_nodes=20, num_edges=40, feature_dim=3)
        
        # 前向传播
        output = gat_layer(data.x, data.edge_index, node_mask=data.node_mask)
        
        print(f"输入形状: {data.x.shape}")
        print(f"输出形状: {output.shape}")
        print(f"有效节点数: {data.node_mask.sum().item()}")
        
        # 验证输出
        expected_output_dim = 16 * 4  # out_channels * heads
        assert output.shape == (20, expected_output_dim), f"输出形状不正确: {output.shape}"
        assert not torch.isnan(output).any(), "输出包含NaN"
        assert not torch.isinf(output).any(), "输出包含无穷值"
        
        print("✓ GAT层测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GAT层测试失败: {e}")
        return False

def test_graph_norm():
    """测试图归一化"""
    if not TORCH_AVAILABLE:
        print("跳过图归一化测试（PyTorch不可用）")
        return False
    
    print("\n=== 测试图归一化 ===")
    
    try:
        # 创建图归一化层
        norm_layer = GraphNorm(num_features=64)
        
        # 创建测试数据
        x = torch.randn(20, 64)
        node_mask = torch.ones(20, dtype=torch.bool)
        node_mask[15:] = False  # 模拟变长图
        
        # 前向传播
        output = norm_layer(x, node_mask=node_mask)
        
        print(f"输入形状: {x.shape}")
        print(f"输出形状: {output.shape}")
        
        # 验证归一化效果（只对有效节点）
        valid_output = output[node_mask]
        mean = valid_output.mean(dim=0)
        var = valid_output.var(dim=0, unbiased=False)
        
        print(f"有效节点均值范围: [{mean.min().item():.6f}, {mean.max().item():.6f}]")
        print(f"有效节点方差范围: [{var.min().item():.6f}, {var.max().item():.6f}]")
        
        # 验证归一化正确性
        assert torch.allclose(mean, torch.zeros_like(mean), atol=1e-5), "均值不为0"
        assert torch.allclose(var, torch.ones_like(var), atol=1e-5), "方差不为1"
        
        print("✓ 图归一化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 图归一化测试失败: {e}")
        return False

def test_gat_encoder():
    """测试GAT编码器"""
    if not TORCH_AVAILABLE:
        print("跳过GAT编码器测试（PyTorch不可用）")
        return False
    
    print("\n=== 测试GAT编码器 ===")
    
    try:
        # 创建GAT编码器
        encoder = GATEncoder(
            input_dim=3,
            hidden_dim=64,
            output_dim=256,
            num_layers=3,
            num_heads=4,
            dropout=0.1,
            pooling_method='attention'
        )
        
        print(f"编码器参数数量: {sum(p.numel() for p in encoder.parameters()):,}")
        
        # 单图测试
        data = create_test_graph(num_nodes=20, num_edges=40, feature_dim=3)
        
        # 前向传播
        graph_embedding = encoder(data.x, data.edge_index, node_mask=data.node_mask)
        
        print(f"单图输入: 节点{data.x.shape}, 边{data.edge_index.shape}")
        print(f"单图输出: {graph_embedding.shape}")
        
        # 验证输出
        assert graph_embedding.shape == (256,), f"单图输出形状不正确: {graph_embedding.shape}"
        assert not torch.isnan(graph_embedding).any(), "单图输出包含NaN"
        
        # 批处理测试
        data_list = [create_test_graph(num_nodes=np.random.randint(10, 25), 
                                     num_edges=np.random.randint(15, 50), 
                                     feature_dim=3) for _ in range(4)]
        batch = Batch.from_data_list(data_list)
        
        batch_embedding = encoder(batch.x, batch.edge_index, batch=batch.batch, 
                                node_mask=batch.node_mask)
        
        print(f"批处理输入: 节点{batch.x.shape}, 边{batch.edge_index.shape}, 批大小{batch.batch.max().item() + 1}")
        print(f"批处理输出: {batch_embedding.shape}")
        
        # 验证批处理输出
        assert batch_embedding.shape == (4, 256), f"批处理输出形状不正确: {batch_embedding.shape}"
        assert not torch.isnan(batch_embedding).any(), "批处理输出包含NaN"
        
        print("✓ GAT编码器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GAT编码器测试失败: {e}")
        return False

def test_multiscale_encoder():
    """测试多尺度编码器"""
    if not TORCH_AVAILABLE:
        print("跳过多尺度编码器测试（PyTorch不可用）")
        return False
    
    print("\n=== 测试多尺度编码器 ===")
    
    try:
        # 创建多尺度编码器
        encoder = MultiScaleGATEncoder(
            input_dim=3,
            hidden_dims=[32, 64, 128],
            output_dim=256,
            num_heads=4,
            dropout=0.1
        )
        
        print(f"多尺度编码器参数数量: {sum(p.numel() for p in encoder.parameters()):,}")
        
        # 测试数据
        data = create_test_graph(num_nodes=20, num_edges=40, feature_dim=3)
        
        # 前向传播
        output = encoder(data.x, data.edge_index, node_mask=data.node_mask)
        
        print(f"输入: 节点{data.x.shape}, 边{data.edge_index.shape}")
        print(f"输出: {output.shape}")
        
        # 验证输出
        assert output.shape == (256,), f"输出形状不正确: {output.shape}"
        assert not torch.isnan(output).any(), "输出包含NaN"
        
        print("✓ 多尺度编码器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 多尺度编码器测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    if not TORCH_AVAILABLE:
        print("跳过性能测试（PyTorch不可用）")
        return False
    
    print("\n=== 性能测试 ===")
    
    try:
        # 创建编码器
        encoder = GATEncoder(
            input_dim=3,
            hidden_dim=128,
            output_dim=256,
            num_layers=4,
            num_heads=8,
            dropout=0.1
        )
        
        # 创建较大的测试数据
        data_list = [create_test_graph(num_nodes=np.random.randint(20, 40), 
                                     num_edges=np.random.randint(30, 80), 
                                     feature_dim=3) for _ in range(8)]
        batch = Batch.from_data_list(data_list)
        
        # 预热
        with torch.no_grad():
            _ = encoder(batch.x, batch.edge_index, batch=batch.batch, node_mask=batch.node_mask)
        
        # 性能测试
        num_runs = 10
        start_time = time.time()
        
        for _ in range(num_runs):
            with torch.no_grad():
                output = encoder(batch.x, batch.edge_index, batch=batch.batch, node_mask=batch.node_mask)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / num_runs
        
        print(f"批大小: {batch.batch.max().item() + 1}")
        print(f"总节点数: {batch.x.shape[0]}")
        print(f"总边数: {batch.edge_index.shape[1]}")
        print(f"平均推理时间: {avg_time:.4f}秒")
        print(f"吞吐量: {batch.batch.max().item() + 1 / avg_time:.2f} 图/秒")
        
        print("✓ 性能测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False

def test_gradient_flow():
    """测试梯度流"""
    if not TORCH_AVAILABLE:
        print("跳过梯度流测试（PyTorch不可用）")
        return False
    
    print("\n=== 梯度流测试 ===")
    
    try:
        # 创建编码器
        encoder = GATEncoder(
            input_dim=3,
            hidden_dim=64,
            output_dim=128,
            num_layers=3,
            num_heads=4,
            dropout=0.0  # 关闭dropout以便测试梯度
        )
        
        # 创建测试数据
        data = create_test_graph(num_nodes=15, num_edges=30, feature_dim=3)
        
        # 前向传播
        output = encoder(data.x, data.edge_index, node_mask=data.node_mask)
        
        # 计算损失
        target = torch.randn_like(output)
        loss = nn.MSELoss()(output, target)
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        has_grad = 0
        total_params = 0
        grad_norm = 0
        
        for name, param in encoder.named_parameters():
            total_params += 1
            if param.grad is not None:
                has_grad += 1
                grad_norm += param.grad.norm().item()
        
        print(f"总参数数: {total_params}")
        print(f"有梯度的参数数: {has_grad}")
        print(f"梯度范数总和: {grad_norm:.6f}")
        
        # 验证梯度
        assert has_grad == total_params, f"部分参数没有梯度: {has_grad}/{total_params}"
        assert grad_norm > 0, "梯度范数为0"
        assert not np.isnan(grad_norm), "梯度包含NaN"
        
        print("✓ 梯度流测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 梯度流测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始GAT编码器测试...\n")
    
    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过所有测试")
        print("请安装PyTorch和PyTorch Geometric:")
        print("pip install torch torch-geometric")
        return
    
    tests = [
        test_gat_layer,
        test_graph_norm,
        test_gat_encoder,
        test_multiscale_encoder,
        test_performance,
        test_gradient_flow
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！GAT编码器实现正确。")
    else:
        print(f"✗ {total - passed} 个测试失败")
    
    print("\n建议安装依赖:")
    print("pip install torch torch-geometric numpy matplotlib networkx")

if __name__ == "__main__":
    main()
