"""
测试性能预测模块的功能和正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import time

# 尝试导入模型（如果PyTorch可用）
try:
    import torch
    import torch.nn as nn
    from torch_geometric.data import Data, Batch
    
    from models.predictor import MLPPredictor, PerformancePredictor, AdaptivePerformancePredictor
    from models.graph_encoder import GATEncoder
    from training.losses import WeightedMSELoss, AdaptiveWeightedLoss, MultiTaskLoss, PerformanceLossManager
    from training.metrics import PerformanceMetrics, MetricsVisualizer
    
    TORCH_AVAILABLE = True
except ImportError as e:
    print(f"PyTorch相关模块导入失败: {e}")
    TORCH_AVAILABLE = False

def create_mock_encoder():
    """创建模拟编码器"""
    if not TORCH_AVAILABLE:
        return None
    
    return GATEncoder(
        input_dim=3,
        hidden_dim=64,
        output_dim=128,
        num_layers=2,
        num_heads=4,
        dropout=0.1
    )

def create_test_data(batch_size: int = 4, num_features: int = 18):
    """创建测试数据"""
    if not TORCH_AVAILABLE:
        return None, None, None, None
    
    # 创建图数据
    graphs = []
    for _ in range(batch_size):
        num_nodes = np.random.randint(10, 20)
        x = torch.randn(num_nodes, 3)
        edge_index = torch.randint(0, num_nodes, (2, num_nodes * 2))
        
        # 节点mask
        valid_nodes = np.random.randint(8, num_nodes + 1)
        node_mask = torch.zeros(num_nodes, dtype=torch.bool)
        node_mask[:valid_nodes] = True
        
        data = Data(x=x, edge_index=edge_index, node_mask=node_mask)
        graphs.append(data)
    
    batch = Batch.from_data_list(graphs)
    
    # 创建目标性能数据
    targets = torch.randn(batch_size, num_features)
    
    return batch.x, batch.edge_index, batch.batch, batch.node_mask, targets

def test_mlp_predictor():
    """测试MLP预测器"""
    if not TORCH_AVAILABLE:
        print("跳过MLP预测器测试（PyTorch不可用）")
        return False
    
    print("=== 测试MLP预测器 ===")
    
    try:
        # 创建预测器
        predictor = MLPPredictor(
            input_dim=128,
            output_dim=18,
            hidden_dims=[64, 32],
            activation='relu',
            dropout=0.1,
            use_batch_norm=True
        )
        
        print(f"预测器参数数量: {sum(p.numel() for p in predictor.parameters()):,}")
        
        # 测试前向传播
        batch_size = 8
        input_tensor = torch.randn(batch_size, 128)
        
        output = predictor(input_tensor)
        
        print(f"输入形状: {input_tensor.shape}")
        print(f"输出形状: {output.shape}")
        
        # 验证输出
        assert output.shape == (batch_size, 18), f"输出形状不正确: {output.shape}"
        assert not torch.isnan(output).any(), "输出包含NaN"
        assert not torch.isinf(output).any(), "输出包含无穷值"
        
        print("✓ MLP预测器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ MLP预测器测试失败: {e}")
        return False

def test_performance_predictor():
    """测试性能预测器"""
    if not TORCH_AVAILABLE:
        print("跳过性能预测器测试（PyTorch不可用）")
        return False
    
    print("\n=== 测试性能预测器 ===")
    
    try:
        # 创建编码器和预测器
        encoder = create_mock_encoder()
        predictor = PerformancePredictor(
            graph_encoder=encoder,
            num_performance_features=18,
            predictor_config={
                'hidden_dims': [64, 32],
                'activation': 'relu',
                'dropout': 0.1
            }
        )
        
        print(f"完整预测器参数数量: {sum(p.numel() for p in predictor.parameters()):,}")
        
        # 创建测试数据
        x, edge_index, batch, node_mask, targets = create_test_data(batch_size=4, num_features=18)
        
        # 前向传播
        predictions = predictor(x, edge_index, batch, node_mask)
        
        print(f"输入: 节点{x.shape}, 边{edge_index.shape}, 批大小{batch.max().item() + 1}")
        print(f"预测输出: {predictions.shape}")
        print(f"目标形状: {targets.shape}")
        
        # 验证输出
        assert predictions.shape == targets.shape, f"预测形状不匹配: {predictions.shape} vs {targets.shape}"
        assert not torch.isnan(predictions).any(), "预测包含NaN"
        
        # 测试不确定性预测
        mean_pred, std_pred = predictor.predict_with_uncertainty(x, edge_index, batch, node_mask, num_samples=5)
        
        print(f"不确定性预测 - 均值: {mean_pred.shape}, 标准差: {std_pred.shape}")
        
        assert mean_pred.shape == predictions.shape, "不确定性预测均值形状不正确"
        assert std_pred.shape == predictions.shape, "不确定性预测标准差形状不正确"
        assert (std_pred >= 0).all(), "标准差应该非负"
        
        print("✓ 性能预测器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 性能预测器测试失败: {e}")
        return False

def test_adaptive_predictor():
    """测试自适应预测器"""
    if not TORCH_AVAILABLE:
        print("跳过自适应预测器测试（PyTorch不可用）")
        return False
    
    print("\n=== 测试自适应预测器 ===")
    
    try:
        # 创建自适应预测器
        encoder = create_mock_encoder()
        predictor = AdaptivePerformancePredictor(
            graph_encoder=encoder,
            initial_num_features=18
        )
        
        # 测试初始预测
        x, edge_index, batch, node_mask, targets = create_test_data(batch_size=4, num_features=18)
        predictions = predictor(x, edge_index, batch, node_mask)
        
        print(f"初始预测: {predictions.shape}")
        assert predictions.shape == (4, 18), f"初始预测形状不正确: {predictions.shape}"
        
        # 测试维度更新
        new_features = ['mech_Ex', 'mech_Ey', 'mech_Ez', 'mech_Gyz', 'mech_Gxz', 'mech_Gxy']
        predictor.update_output_dimension(len(new_features), new_features)
        
        # 测试更新后的预测
        new_targets = torch.randn(4, len(new_features))
        new_predictions = predictor(x, edge_index, batch, node_mask)
        
        print(f"更新后预测: {new_predictions.shape}")
        assert new_predictions.shape == (4, len(new_features)), f"更新后预测形状不正确: {new_predictions.shape}"
        
        # 测试特征名称
        feature_names = predictor.get_feature_names()
        print(f"特征名称: {feature_names}")
        assert feature_names == new_features, "特征名称不匹配"
        
        print("✓ 自适应预测器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 自适应预测器测试失败: {e}")
        return False

def test_loss_functions():
    """测试损失函数"""
    if not TORCH_AVAILABLE:
        print("跳过损失函数测试（PyTorch不可用）")
        return False
    
    print("\n=== 测试损失函数 ===")
    
    try:
        batch_size, num_features = 8, 18
        pred = torch.randn(batch_size, num_features)
        target = torch.randn(batch_size, num_features)
        
        # 测试加权MSE损失
        weights = torch.rand(num_features)
        weighted_mse = WeightedMSELoss(weights)
        loss1 = weighted_mse(pred, target)
        
        print(f"加权MSE损失: {loss1.item():.6f}")
        assert not torch.isnan(loss1), "加权MSE损失为NaN"
        
        # 测试自适应加权损失
        adaptive_loss = AdaptiveWeightedLoss(num_features, learn_weights=True)
        loss2, details = adaptive_loss(pred, target)
        
        print(f"自适应损失: {loss2.item():.6f}")
        print(f"损失详情键: {list(details.keys())}")
        
        assert not torch.isnan(loss2), "自适应损失为NaN"
        assert 'weights' in details, "损失详情缺少权重"
        assert details['weights'].shape == (num_features,), "权重形状不正确"
        
        # 测试多任务损失
        task_configs = [
            {'name': 'mechanical', 'indices': list(range(12)), 'weight': 1.0},
            {'name': 'scaling', 'indices': list(range(12, 18)), 'weight': 0.5}
        ]
        
        multi_task_loss = MultiTaskLoss(task_configs, uncertainty_weighting=True)
        loss3, details3 = multi_task_loss(pred, target)
        
        print(f"多任务损失: {loss3.item():.6f}")
        print(f"多任务损失详情: {list(details3.keys())}")
        
        assert not torch.isnan(loss3), "多任务损失为NaN"
        assert 'mechanical_loss' in details3, "缺少mechanical任务损失"
        assert 'scaling_loss' in details3, "缺少scaling任务损失"
        
        print("✓ 损失函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 损失函数测试失败: {e}")
        return False

def test_metrics():
    """测试评估指标"""
    if not TORCH_AVAILABLE:
        print("跳过评估指标测试（PyTorch不可用）")
        return False
    
    print("\n=== 测试评估指标 ===")
    
    try:
        # 创建测试数据
        batch_size, num_features = 20, 6
        feature_names = ['mech_Ex', 'mech_Ey', 'mech_Ez', 'mech_Gyz', 'mech_Gxz', 'mech_Gxy']
        
        # 创建有一定相关性的预测和目标
        target = torch.randn(batch_size, num_features)
        noise = torch.randn(batch_size, num_features) * 0.2
        pred = target + noise  # 添加噪声
        
        # 创建评估器
        metrics_calculator = PerformanceMetrics(feature_names)
        
        # 更新数据
        metrics_calculator.update(pred, target)
        
        # 计算指标
        metrics = metrics_calculator.compute_metrics()
        
        print(f"计算的指标数量: {len(metrics)}")
        print(f"整体R²: {metrics.get('r2_overall', 'N/A'):.4f}")
        print(f"整体RMSE: {metrics.get('rmse_overall', 'N/A'):.4f}")
        print(f"平均R²: {metrics.get('r2_mean', 'N/A'):.4f}")
        
        # 验证关键指标
        assert 'r2_overall' in metrics, "缺少整体R²"
        assert 'mse_overall' in metrics, "缺少整体MSE"
        assert 'r2_per_feature' in metrics, "缺少分特征R²"
        assert len(metrics['r2_per_feature']) == num_features, "分特征R²数量不正确"
        
        # 测试最差预测
        worst = metrics_calculator.get_worst_predictions(top_k=3)
        print(f"最差预测数量: {len(worst.get('indices', []))}")
        
        assert len(worst['indices']) <= 3, "最差预测数量超出限制"
        
        print("✓ 评估指标测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 评估指标测试失败: {e}")
        return False

def test_integration():
    """测试集成功能"""
    if not TORCH_AVAILABLE:
        print("跳过集成测试（PyTorch不可用）")
        return False
    
    print("\n=== 测试集成功能 ===")
    
    try:
        # 创建完整的预测流程
        encoder = create_mock_encoder()
        predictor = PerformancePredictor(
            graph_encoder=encoder,
            num_performance_features=18
        )
        
        # 创建损失管理器
        feature_names = [f'feature_{i}' for i in range(18)]
        loss_manager = PerformanceLossManager(
            feature_names=feature_names,
            loss_config={'use_adaptive_weights': True}
        )
        
        # 创建评估器
        metrics_calculator = PerformanceMetrics(feature_names)
        
        # 模拟训练步骤
        x, edge_index, batch, node_mask, targets = create_test_data(batch_size=8, num_features=18)
        
        # 前向传播
        predictions = predictor(x, edge_index, batch, node_mask)
        
        # 计算损失
        loss, loss_details = loss_manager.compute_loss(predictions, targets)
        
        # 更新指标
        metrics_calculator.update(predictions, targets, loss)
        
        # 计算评估指标
        metrics = metrics_calculator.compute_metrics()
        
        print(f"集成测试结果:")
        print(f"  损失: {loss.item():.6f}")
        print(f"  R²: {metrics.get('r2_overall', 'N/A'):.4f}")
        print(f"  RMSE: {metrics.get('rmse_overall', 'N/A'):.4f}")
        
        # 验证
        assert not torch.isnan(loss), "损失为NaN"
        assert 'r2_overall' in metrics, "缺少评估指标"
        
        print("✓ 集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始性能预测模块测试...\n")
    
    if not TORCH_AVAILABLE:
        print("PyTorch不可用，跳过所有测试")
        print("请安装PyTorch和相关依赖:")
        print("pip install torch torch-geometric scikit-learn matplotlib seaborn")
        return
    
    tests = [
        test_mlp_predictor,
        test_performance_predictor,
        test_adaptive_predictor,
        test_loss_functions,
        test_metrics,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！性能预测模块实现正确。")
        
        print("\n📋 实现总结:")
        print("✓ MLPPredictor - 多层感知机预测器")
        print("✓ PerformancePredictor - 完整性能预测模块")
        print("✓ AdaptivePerformancePredictor - 自适应维度预测器")
        print("✓ WeightedMSELoss - 加权均方误差损失")
        print("✓ AdaptiveWeightedLoss - 自适应加权损失")
        print("✓ MultiTaskLoss - 多任务学习损失")
        print("✓ PerformanceMetrics - 完整评估指标")
        print("✓ MetricsVisualizer - 指标可视化工具")
        
        print("\n🔧 核心特性:")
        print("• 动态维度支持（根据lattice.csv自动调整）")
        print("• 多种损失函数（MSE、MAE、Huber、自适应权重）")
        print("• 完整评估指标（R²、RMSE、MAE、MAPE、相关系数）")
        print("• 不确定性量化（Dropout采样）")
        print("• 特征重要性分析")
        print("• 权重迁移和模型适应")
        print("• 可视化分析工具")
        
    else:
        print(f"✗ {total - passed} 个测试失败")
    
    print("\n📦 需要的依赖包:")
    print("pip install torch torch-geometric scikit-learn matplotlib seaborn scipy")

if __name__ == "__main__":
    main()
