# Default Configuration for <PERSON><PERSON>ce GNN-Diffusion Model

# Data Configuration
data:
  node_file: "node.csv"
  edge_file: "edge.csv"
  lattice_file: "lattice.csv"
  max_nodes: 64  # Maximum number of nodes for padding
  train_split: 0.7
  val_split: 0.15
  test_split: 0.15
  random_seed: 42

# Model Configuration
model:
  # Graph Encoder (GAT)
  encoder:
    input_dim: 3  # x, y, z coordinates
    hidden_dim: 256
    output_dim: 512
    num_layers: 4
    num_heads: 8
    dropout: 0.1
    use_residual: true
    use_layer_norm: true
  
  # Performance Predictor
  predictor:
    input_dim: 512  # From encoder output
    hidden_dims: [256, 128, 64]
    output_dim: 25  # 25 performance metrics
    dropout: 0.2
    activation: "relu"
  
  # Diffusion Model
  diffusion:
    timesteps: 1000
    beta_schedule: "cosine"  # linear, cosine
    beta_start: 0.0001
    beta_end: 0.02
    condition_dim: 25  # Performance condition dimension
    denoising_dim: 512
    unet_channels: [128, 256, 512, 256, 128]

# Training Configuration
training:
  batch_size: 8
  learning_rate: 0.001
  weight_decay: 0.0001
  num_epochs: 200
  patience: 20  # Early stopping patience
  
  # Loss weights
  loss_weights:
    prediction: 1.0
    diffusion: 1.0
    cycle_consistency: 0.1
    physical_constraint: 0.5
  
  # Optimizer
  optimizer: "adam"
  scheduler: "cosine"
  warmup_epochs: 10

# Physical Constraints
constraints:
  min_distance: 0.1  # Minimum distance between nodes
  max_distance: 2.0  # Maximum edge length
  connectivity_check: true
  symmetry_preservation: true

# Evaluation
evaluation:
  metrics: ["r2", "mae", "rmse", "mape"]
  cross_validation: 5
  save_predictions: true
  visualize_results: true

# Logging and Checkpoints
logging:
  log_level: "INFO"
  log_dir: "logs"
  checkpoint_dir: "checkpoints"
  save_every: 10  # Save checkpoint every N epochs
  tensorboard: true
  wandb: false  # Set to true if using Weights & Biases

# Hardware
device: "auto"  # auto, cpu, cuda
mixed_precision: true
num_workers: 4
