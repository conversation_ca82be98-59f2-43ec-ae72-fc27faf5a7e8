"""
图操作工具函数：图处理、分析和可视化相关工具
"""

import torch
import numpy as np
from torch_geometric.data import Data, Batch
from torch_geometric.utils import to_networkx, from_networkx
import networkx as nx
from typing import List, Tuple, Optional, Dict, Any
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def create_batch_from_data_list(data_list: List[Data]) -> Batch:
    """
    从数据列表创建批处理
    
    Args:
        data_list: PyTorch Geometric Data对象列表
        
    Returns:
        批处理对象
    """
    return Batch.from_data_list(data_list)

def extract_subgraph(data: Data, node_indices: torch.Tensor) -> Data:
    """
    提取子图
    
    Args:
        data: 原始图数据
        node_indices: 要保留的节点索引
        
    Returns:
        子图数据
    """
    # 创建节点映射
    node_map = {old_idx.item(): new_idx for new_idx, old_idx in enumerate(node_indices)}
    
    # 提取节点特征
    x = data.x[node_indices]
    
    # 提取边
    edge_mask = torch.isin(data.edge_index[0], node_indices) & torch.isin(data.edge_index[1], node_indices)
    edge_index = data.edge_index[:, edge_mask]
    
    # 重新映射边索引
    for i in range(edge_index.shape[1]):
        edge_index[0, i] = node_map[edge_index[0, i].item()]
        edge_index[1, i] = node_map[edge_index[1, i].item()]
    
    # 创建新的数据对象
    subgraph = Data(x=x, edge_index=edge_index)
    
    # 复制其他属性
    for key, value in data:
        if key not in ['x', 'edge_index']:
            if isinstance(value, torch.Tensor) and value.size(0) == data.x.size(0):
                # 节点级别属性
                setattr(subgraph, key, value[node_indices])
            else:
                # 图级别属性
                setattr(subgraph, key, value)
    
    return subgraph

def compute_graph_statistics(data: Data, node_mask: Optional[torch.Tensor] = None) -> Dict[str, Any]:
    """
    计算图统计信息
    
    Args:
        data: 图数据
        node_mask: 节点mask
        
    Returns:
        统计信息字典
    """
    if node_mask is not None:
        num_nodes = node_mask.sum().item()
        valid_edges = torch.isin(data.edge_index[0], torch.where(node_mask)[0]) & \
                     torch.isin(data.edge_index[1], torch.where(node_mask)[0])
        num_edges = valid_edges.sum().item()
    else:
        num_nodes = data.x.size(0)
        num_edges = data.edge_index.size(1)
    
    # 转换为NetworkX图进行分析
    G = to_networkx(data, to_undirected=True)
    
    if node_mask is not None:
        # 移除无效节点
        invalid_nodes = [i for i in range(data.x.size(0)) if not node_mask[i]]
        G.remove_nodes_from(invalid_nodes)
    
    stats = {
        'num_nodes': num_nodes,
        'num_edges': num_edges // 2,  # 无向图
        'density': nx.density(G) if num_nodes > 1 else 0,
        'is_connected': nx.is_connected(G),
        'num_components': nx.number_connected_components(G),
    }
    
    if nx.is_connected(G) and num_nodes > 2:
        stats.update({
            'diameter': nx.diameter(G),
            'average_clustering': nx.average_clustering(G),
            'average_shortest_path_length': nx.average_shortest_path_length(G)
        })
    
    # 度统计
    degrees = [G.degree(n) for n in G.nodes()]
    if degrees:
        stats.update({
            'avg_degree': np.mean(degrees),
            'max_degree': max(degrees),
            'min_degree': min(degrees),
            'degree_std': np.std(degrees)
        })
    
    return stats

def visualize_graph_3d(data: Data, node_mask: Optional[torch.Tensor] = None,
                      save_path: Optional[str] = None, title: str = "3D Graph Visualization",
                      node_size: float = 50, edge_width: float = 1.0,
                      node_color: str = 'red', edge_color: str = 'blue') -> plt.Figure:
    """
    3D图可视化
    
    Args:
        data: 图数据
        node_mask: 节点mask
        save_path: 保存路径
        title: 图标题
        node_size: 节点大小
        edge_width: 边宽度
        node_color: 节点颜色
        edge_color: 边颜色
        
    Returns:
        matplotlib图形对象
    """
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 获取节点坐标
    coords = data.x[:, :3].numpy()  # 假设前3维是坐标
    
    # 应用节点mask
    if node_mask is not None:
        valid_indices = torch.where(node_mask)[0].numpy()
        coords = coords[valid_indices]
        
        # 过滤边
        edge_index = data.edge_index.numpy()
        valid_edges = []
        node_map = {old_idx: new_idx for new_idx, old_idx in enumerate(valid_indices)}
        
        for i in range(edge_index.shape[1]):
            src, tgt = edge_index[0, i], edge_index[1, i]
            if src in node_map and tgt in node_map:
                valid_edges.append([node_map[src], node_map[tgt]])
        
        if valid_edges:
            edge_index = np.array(valid_edges).T
        else:
            edge_index = np.array([[], []])
    else:
        edge_index = data.edge_index.numpy()
    
    # 绘制节点
    ax.scatter(coords[:, 0], coords[:, 1], coords[:, 2], 
              c=node_color, s=node_size, alpha=0.8)
    
    # 绘制边
    for i in range(edge_index.shape[1]):
        src, tgt = edge_index[0, i], edge_index[1, i]
        if src < len(coords) and tgt < len(coords):
            ax.plot([coords[src, 0], coords[tgt, 0]],
                   [coords[src, 1], coords[tgt, 1]],
                   [coords[src, 2], coords[tgt, 2]],
                   c=edge_color, linewidth=edge_width, alpha=0.6)
    
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title(title)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig

def compute_node_centrality(data: Data, node_mask: Optional[torch.Tensor] = None,
                          centrality_type: str = 'betweenness') -> torch.Tensor:
    """
    计算节点中心性
    
    Args:
        data: 图数据
        node_mask: 节点mask
        centrality_type: 中心性类型 ('betweenness', 'closeness', 'degree', 'eigenvector')
        
    Returns:
        节点中心性值
    """
    G = to_networkx(data, to_undirected=True)
    
    if node_mask is not None:
        invalid_nodes = [i for i in range(data.x.size(0)) if not node_mask[i]]
        G.remove_nodes_from(invalid_nodes)
    
    if centrality_type == 'betweenness':
        centrality = nx.betweenness_centrality(G)
    elif centrality_type == 'closeness':
        centrality = nx.closeness_centrality(G)
    elif centrality_type == 'degree':
        centrality = nx.degree_centrality(G)
    elif centrality_type == 'eigenvector':
        try:
            centrality = nx.eigenvector_centrality(G, max_iter=1000)
        except:
            centrality = nx.degree_centrality(G)  # 回退到度中心性
    else:
        raise ValueError(f"Unknown centrality type: {centrality_type}")
    
    # 转换为张量
    centrality_tensor = torch.zeros(data.x.size(0))
    for node, value in centrality.items():
        centrality_tensor[node] = value
    
    return centrality_tensor

def add_edge_features(data: Data, feature_type: str = 'distance') -> Data:
    """
    添加边特征
    
    Args:
        data: 图数据
        feature_type: 特征类型 ('distance', 'angle', 'both')
        
    Returns:
        带边特征的图数据
    """
    edge_index = data.edge_index
    node_coords = data.x[:, :3]  # 假设前3维是坐标
    
    edge_features = []
    
    for i in range(edge_index.size(1)):
        src, tgt = edge_index[0, i], edge_index[1, i]
        src_coord = node_coords[src]
        tgt_coord = node_coords[tgt]
        
        if feature_type in ['distance', 'both']:
            # 欧几里得距离
            distance = torch.norm(src_coord - tgt_coord)
            edge_features.append([distance.item()])
        
        if feature_type in ['angle', 'both']:
            # 这里可以添加角度特征的计算
            # 简化处理，添加零特征
            if feature_type == 'angle':
                edge_features.append([0.0])
            else:
                edge_features[-1].append(0.0)
    
    data.edge_attr = torch.tensor(edge_features, dtype=torch.float32)
    return data

def normalize_graph_coordinates(data: Data, method: str = 'unit_sphere',
                              node_mask: Optional[torch.Tensor] = None) -> Data:
    """
    标准化图坐标
    
    Args:
        data: 图数据
        method: 标准化方法 ('unit_sphere', 'unit_cube', 'zero_mean')
        node_mask: 节点mask
        
    Returns:
        标准化后的图数据
    """
    coords = data.x[:, :3].clone()  # 假设前3维是坐标
    
    if node_mask is not None:
        valid_coords = coords[node_mask]
    else:
        valid_coords = coords
    
    if method == 'unit_sphere':
        center = valid_coords.mean(dim=0)
        coords_centered = coords - center
        max_dist = torch.norm(coords_centered[node_mask] if node_mask is not None else coords_centered, dim=1).max()
        if max_dist > 0:
            coords = coords_centered / max_dist
        else:
            coords = coords_centered
    
    elif method == 'unit_cube':
        min_vals = valid_coords.min(dim=0)[0]
        max_vals = valid_coords.max(dim=0)[0]
        ranges = max_vals - min_vals
        ranges[ranges == 0] = 1
        coords = (coords - min_vals) / ranges
    
    elif method == 'zero_mean':
        mean = valid_coords.mean(dim=0)
        std = valid_coords.std(dim=0)
        std[std == 0] = 1
        coords = (coords - mean) / std
    
    # 更新坐标
    data.x[:, :3] = coords
    
    return data

def check_graph_connectivity(data: Data, node_mask: Optional[torch.Tensor] = None) -> bool:
    """
    检查图连通性
    
    Args:
        data: 图数据
        node_mask: 节点mask
        
    Returns:
        是否连通
    """
    G = to_networkx(data, to_undirected=True)
    
    if node_mask is not None:
        invalid_nodes = [i for i in range(data.x.size(0)) if not node_mask[i]]
        G.remove_nodes_from(invalid_nodes)
    
    return nx.is_connected(G)

def compute_graph_laplacian(data: Data, node_mask: Optional[torch.Tensor] = None,
                          normalization: str = 'sym') -> torch.Tensor:
    """
    计算图拉普拉斯矩阵
    
    Args:
        data: 图数据
        node_mask: 节点mask
        normalization: 归一化类型 ('sym', 'rw', 'none')
        
    Returns:
        拉普拉斯矩阵
    """
    from torch_geometric.utils import get_laplacian
    
    edge_index = data.edge_index
    num_nodes = data.x.size(0)
    
    if node_mask is not None:
        # 过滤边
        valid_nodes = torch.where(node_mask)[0]
        edge_mask = torch.isin(edge_index[0], valid_nodes) & torch.isin(edge_index[1], valid_nodes)
        edge_index = edge_index[:, edge_mask]
        num_nodes = node_mask.sum().item()
    
    edge_index, edge_weight = get_laplacian(edge_index, normalization=normalization, num_nodes=num_nodes)
    
    # 转换为密集矩阵
    laplacian = torch.sparse_coo_tensor(edge_index, edge_weight, (num_nodes, num_nodes)).to_dense()
    
    return laplacian
