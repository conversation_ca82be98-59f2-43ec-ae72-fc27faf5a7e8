"""
自定义图神经网络层：GAT层和相关组件
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import MessagePassing
from torch_geometric.utils import add_self_loops, degree, softmax
from torch_geometric.typing import Adj, OptTensor, PairTensor
from typing import Union, Tuple, Optional
import math

class MultiHeadGATLayer(MessagePassing):
    """多头图注意力层"""
    
    def __init__(self, in_channels: int, out_channels: int, heads: int = 8,
                 concat: bool = True, negative_slope: float = 0.2,
                 dropout: float = 0.0, add_self_loops: bool = True,
                 bias: bool = True, **kwargs):
        """
        初始化多头GAT层
        
        Args:
            in_channels: 输入特征维度
            out_channels: 输出特征维度
            heads: 注意力头数
            concat: 是否连接多头输出
            negative_slope: LeakyReLU负斜率
            dropout: Dropout概率
            add_self_loops: 是否添加自环
            bias: 是否使用偏置
        """
        kwargs.setdefault('aggr', 'add')
        super().__init__(node_dim=0, **kwargs)
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.heads = heads
        self.concat = concat
        self.negative_slope = negative_slope
        self.dropout = dropout
        self.add_self_loops = add_self_loops
        
        # 线性变换层
        self.lin_l = nn.Linear(in_channels, heads * out_channels, bias=False)
        self.lin_r = self.lin_l
        
        # 注意力参数
        self.att_l = nn.Parameter(torch.Tensor(1, heads, out_channels))
        self.att_r = nn.Parameter(torch.Tensor(1, heads, out_channels))
        
        if bias and concat:
            self.bias = nn.Parameter(torch.Tensor(heads * out_channels))
        elif bias and not concat:
            self.bias = nn.Parameter(torch.Tensor(out_channels))
        else:
            self.register_parameter('bias', None)
        
        self.reset_parameters()
    
    def reset_parameters(self):
        """重置参数"""
        nn.init.xavier_uniform_(self.lin_l.weight)
        nn.init.xavier_uniform_(self.att_l)
        nn.init.xavier_uniform_(self.att_r)
        if self.bias is not None:
            nn.init.zeros_(self.bias)
    
    def forward(self, x: Union[torch.Tensor, PairTensor], edge_index: Adj,
                node_mask: OptTensor = None, size: Optional[Tuple[int, int]] = None,
                return_attention_weights: bool = False):
        """
        前向传播
        
        Args:
            x: 节点特征
            edge_index: 边索引
            node_mask: 节点mask（用于变长图）
            size: 图大小
            return_attention_weights: 是否返回注意力权重
        """
        H, C = self.heads, self.out_channels
        
        # 线性变换
        if isinstance(x, torch.Tensor):
            x_l = x_r = self.lin_l(x).view(-1, H, C)
        else:
            x_l, x_r = x[0], x[1]
            x_l = self.lin_l(x_l).view(-1, H, C)
            x_r = self.lin_r(x_r).view(-1, H, C)
        
        # 计算注意力系数
        alpha_l = (x_l * self.att_l).sum(dim=-1)
        alpha_r = (x_r * self.att_r).sum(dim=-1)
        
        # 添加自环
        if self.add_self_loops:
            if isinstance(edge_index, torch.Tensor):
                num_nodes = x_l.size(0)
                edge_index, _ = add_self_loops(edge_index, num_nodes=num_nodes)
            else:
                raise NotImplementedError
        
        # 消息传递
        out = self.propagate(edge_index, x=(x_l, x_r), alpha=(alpha_l, alpha_r),
                           size=size, node_mask=node_mask)
        
        # 处理输出
        if self.concat:
            out = out.view(-1, self.heads * self.out_channels)
        else:
            out = out.mean(dim=1)
        
        if self.bias is not None:
            out += self.bias
        
        # 应用节点mask
        if node_mask is not None:
            out = out * node_mask.unsqueeze(-1).float()
        
        if return_attention_weights:
            # 这里简化处理，实际实现需要收集注意力权重
            return out, None
        else:
            return out
    
    def message(self, x_j: torch.Tensor, alpha_j: torch.Tensor, alpha_i: torch.Tensor,
                index: torch.Tensor, ptr: OptTensor, size_i: Optional[int]) -> torch.Tensor:
        """消息函数"""
        alpha = alpha_j + alpha_i
        alpha = F.leaky_relu(alpha, self.negative_slope)
        alpha = softmax(alpha, index, ptr, size_i)
        alpha = F.dropout(alpha, p=self.dropout, training=self.training)
        return x_j * alpha.unsqueeze(-1)

class GraphNorm(nn.Module):
    """图归一化层"""
    
    def __init__(self, num_features: int, eps: float = 1e-5, affine: bool = True):
        """
        初始化图归一化
        
        Args:
            num_features: 特征维度
            eps: 数值稳定性参数
            affine: 是否使用可学习的仿射变换
        """
        super().__init__()
        self.num_features = num_features
        self.eps = eps
        self.affine = affine
        
        if self.affine:
            self.weight = nn.Parameter(torch.ones(num_features))
            self.bias = nn.Parameter(torch.zeros(num_features))
        else:
            self.register_parameter('weight', None)
            self.register_parameter('bias', None)
    
    def forward(self, x: torch.Tensor, batch: OptTensor = None,
                node_mask: OptTensor = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征 [num_nodes, num_features]
            batch: 批索引
            node_mask: 节点mask
        """
        if batch is None:
            # 单图情况
            if node_mask is not None:
                # 只对有效节点计算统计量
                valid_x = x[node_mask]
                mean = valid_x.mean(dim=0, keepdim=True)
                var = valid_x.var(dim=0, keepdim=True, unbiased=False)
            else:
                mean = x.mean(dim=0, keepdim=True)
                var = x.var(dim=0, keepdim=True, unbiased=False)
        else:
            # 批处理情况
            mean_list = []
            var_list = []
            for i in range(batch.max().item() + 1):
                mask = batch == i
                if node_mask is not None:
                    mask = mask & node_mask
                if mask.sum() > 0:
                    graph_x = x[mask]
                    mean_list.append(graph_x.mean(dim=0))
                    var_list.append(graph_x.var(dim=0, unbiased=False))
                else:
                    mean_list.append(torch.zeros(self.num_features, device=x.device))
                    var_list.append(torch.ones(self.num_features, device=x.device))
            
            mean = torch.stack(mean_list)[batch]
            var = torch.stack(var_list)[batch]
        
        # 归一化
        x_norm = (x - mean) / torch.sqrt(var + self.eps)
        
        # 仿射变换
        if self.affine:
            x_norm = x_norm * self.weight + self.bias
        
        # 应用节点mask
        if node_mask is not None:
            x_norm = x_norm * node_mask.unsqueeze(-1).float()
        
        return x_norm

class ResidualBlock(nn.Module):
    """残差块"""
    
    def __init__(self, layer: nn.Module, dropout: float = 0.1):
        """
        初始化残差块
        
        Args:
            layer: 主要层
            dropout: Dropout概率
        """
        super().__init__()
        self.layer = layer
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x: torch.Tensor, *args, **kwargs) -> torch.Tensor:
        """前向传播"""
        residual = x
        out = self.layer(x, *args, **kwargs)
        out = self.dropout(out)
        
        # 残差连接（如果维度匹配）
        if residual.shape == out.shape:
            out = out + residual
        
        return out

class PositionalEncoding(nn.Module):
    """位置编码（用于图结构）"""
    
    def __init__(self, d_model: int, max_nodes: int = 64):
        """
        初始化位置编码
        
        Args:
            d_model: 模型维度
            max_nodes: 最大节点数
        """
        super().__init__()
        self.d_model = d_model
        
        # 创建位置编码表
        pe = torch.zeros(max_nodes, d_model)
        position = torch.arange(0, max_nodes, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor, node_ids: OptTensor = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征 [num_nodes, d_model]
            node_ids: 节点ID（如果为None，使用顺序ID）
        """
        if node_ids is None:
            node_ids = torch.arange(x.size(0), device=x.device)
        
        # 添加位置编码
        x = x + self.pe[node_ids]
        return x
