"""
条件扩散模型核心实现：DDPM (Denoising Diffusion Probabilistic Models)
支持基于性能需求的点阵结构生成
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Optional, Tuple, Dict, Any, List, Union
from abc import ABC, abstractmethod

class NoiseScheduler:
    """噪声调度器：管理扩散过程中的噪声水平"""
    
    def __init__(self, num_timesteps: int = 1000, 
                 beta_start: float = 0.0001, 
                 beta_end: float = 0.02,
                 schedule_type: str = 'linear'):
        """
        初始化噪声调度器
        
        Args:
            num_timesteps: 扩散步数
            beta_start: 起始噪声方差
            beta_end: 结束噪声方差
            schedule_type: 调度类型 ('linear', 'cosine', 'quadratic')
        """
        self.num_timesteps = num_timesteps
        self.beta_start = beta_start
        self.beta_end = beta_end
        self.schedule_type = schedule_type
        
        # 计算噪声调度
        self.betas = self._get_beta_schedule()
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.alphas_cumprod_prev = F.pad(self.alphas_cumprod[:-1], (1, 0), value=1.0)
        
        # 预计算常用项
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)
        self.log_one_minus_alphas_cumprod = torch.log(1.0 - self.alphas_cumprod)
        self.sqrt_recip_alphas_cumprod = torch.sqrt(1.0 / self.alphas_cumprod)
        self.sqrt_recipm1_alphas_cumprod = torch.sqrt(1.0 / self.alphas_cumprod - 1)
        
        # 后验方差
        self.posterior_variance = (
            self.betas * (1.0 - self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod)
        )
        self.posterior_log_variance_clipped = torch.log(
            torch.cat([self.posterior_variance[1:2], self.posterior_variance[1:]])
        )
        self.posterior_mean_coef1 = (
            self.betas * torch.sqrt(self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod)
        )
        self.posterior_mean_coef2 = (
            (1.0 - self.alphas_cumprod_prev) * torch.sqrt(self.alphas) / (1.0 - self.alphas_cumprod)
        )
    
    def _get_beta_schedule(self) -> torch.Tensor:
        """获取beta调度"""
        if self.schedule_type == 'linear':
            return torch.linspace(self.beta_start, self.beta_end, self.num_timesteps)
        
        elif self.schedule_type == 'cosine':
            # Cosine schedule from "Improved Denoising Diffusion Probabilistic Models"
            s = 0.008
            steps = self.num_timesteps + 1
            x = torch.linspace(0, self.num_timesteps, steps)
            alphas_cumprod = torch.cos(((x / self.num_timesteps) + s) / (1 + s) * math.pi * 0.5) ** 2
            alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
            betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
            return torch.clip(betas, 0, 0.999)
        
        elif self.schedule_type == 'quadratic':
            return torch.linspace(self.beta_start**0.5, self.beta_end**0.5, self.num_timesteps) ** 2
        
        else:
            raise ValueError(f"Unknown schedule type: {self.schedule_type}")
    
    def q_sample(self, x_start: torch.Tensor, t: torch.Tensor, 
                 noise: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向扩散过程：q(x_t | x_0)
        
        Args:
            x_start: 原始数据 [batch_size, ...]
            t: 时间步 [batch_size]
            noise: 噪声（可选）
            
        Returns:
            加噪后的数据 x_t
        """
        if noise is None:
            noise = torch.randn_like(x_start)
        
        sqrt_alphas_cumprod_t = self._extract(self.sqrt_alphas_cumprod, t, x_start.shape)
        sqrt_one_minus_alphas_cumprod_t = self._extract(
            self.sqrt_one_minus_alphas_cumprod, t, x_start.shape
        )
        
        return sqrt_alphas_cumprod_t * x_start + sqrt_one_minus_alphas_cumprod_t * noise
    
    def q_posterior_mean_variance(self, x_start: torch.Tensor, x_t: torch.Tensor, 
                                  t: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        计算后验分布 q(x_{t-1} | x_t, x_0) 的均值和方差
        
        Args:
            x_start: 原始数据
            x_t: 时间t的数据
            t: 时间步
            
        Returns:
            (后验均值, 后验方差, 后验对数方差)
        """
        posterior_mean_coef1_t = self._extract(self.posterior_mean_coef1, t, x_t.shape)
        posterior_mean_coef2_t = self._extract(self.posterior_mean_coef2, t, x_t.shape)
        posterior_variance_t = self._extract(self.posterior_variance, t, x_t.shape)
        posterior_log_variance_clipped_t = self._extract(
            self.posterior_log_variance_clipped, t, x_t.shape
        )
        
        posterior_mean = (
            posterior_mean_coef1_t * x_start + posterior_mean_coef2_t * x_t
        )
        
        return posterior_mean, posterior_variance_t, posterior_log_variance_clipped_t
    
    def predict_start_from_noise(self, x_t: torch.Tensor, t: torch.Tensor, 
                                 noise: torch.Tensor) -> torch.Tensor:
        """
        从噪声预测原始数据：x_0 = (x_t - sqrt(1-α̅_t) * ε) / sqrt(α̅_t)
        
        Args:
            x_t: 时间t的数据
            t: 时间步
            noise: 预测的噪声
            
        Returns:
            预测的原始数据 x_0
        """
        sqrt_recip_alphas_cumprod_t = self._extract(
            self.sqrt_recip_alphas_cumprod, t, x_t.shape
        )
        sqrt_recipm1_alphas_cumprod_t = self._extract(
            self.sqrt_recipm1_alphas_cumprod, t, x_t.shape
        )
        
        return sqrt_recip_alphas_cumprod_t * x_t - sqrt_recipm1_alphas_cumprod_t * noise
    
    def p_mean_variance(self, model_output: torch.Tensor, x_t: torch.Tensor, 
                       t: torch.Tensor, clip_denoised: bool = True) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        计算反向过程的均值和方差
        
        Args:
            model_output: 模型输出（预测的噪声）
            x_t: 时间t的数据
            t: 时间步
            clip_denoised: 是否裁剪去噪结果
            
        Returns:
            (均值, 方差, 对数方差)
        """
        x_recon = self.predict_start_from_noise(x_t, t, model_output)
        
        if clip_denoised:
            x_recon = torch.clamp(x_recon, -1.0, 1.0)
        
        model_mean, posterior_variance, posterior_log_variance = self.q_posterior_mean_variance(
            x_start=x_recon, x_t=x_t, t=t
        )
        
        return model_mean, posterior_variance, posterior_log_variance
    
    def p_sample(self, model_output: torch.Tensor, x_t: torch.Tensor, 
                 t: torch.Tensor, clip_denoised: bool = True) -> torch.Tensor:
        """
        反向采样一步：p(x_{t-1} | x_t)
        
        Args:
            model_output: 模型输出
            x_t: 时间t的数据
            t: 时间步
            clip_denoised: 是否裁剪
            
        Returns:
            x_{t-1}
        """
        model_mean, _, model_log_variance = self.p_mean_variance(
            model_output, x_t, t, clip_denoised=clip_denoised
        )
        
        noise = torch.randn_like(x_t)
        # 当t=0时不添加噪声
        nonzero_mask = (t != 0).float().reshape(-1, *([1] * (len(x_t.shape) - 1)))
        
        return model_mean + nonzero_mask * torch.exp(0.5 * model_log_variance) * noise
    
    def _extract(self, a: torch.Tensor, t: torch.Tensor, x_shape: torch.Size) -> torch.Tensor:
        """提取时间步对应的值并广播到正确形状"""
        batch_size = t.shape[0]
        out = a.gather(-1, t.cpu())
        return out.reshape(batch_size, *((1,) * (len(x_shape) - 1))).to(t.device)

class ConditionalUNet(nn.Module):
    """条件U-Net：用于扩散模型的去噪网络"""
    
    def __init__(self, input_dim: int, condition_dim: int, 
                 hidden_dims: List[int] = [128, 256, 512],
                 time_embed_dim: int = 128,
                 dropout: float = 0.1):
        """
        初始化条件U-Net
        
        Args:
            input_dim: 输入维度（图特征维度）
            condition_dim: 条件维度（性能特征维度）
            hidden_dims: 隐藏层维度列表
            time_embed_dim: 时间嵌入维度
            dropout: Dropout概率
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.condition_dim = condition_dim
        self.hidden_dims = hidden_dims
        self.time_embed_dim = time_embed_dim
        
        # 时间嵌入
        self.time_embed = nn.Sequential(
            nn.Linear(1, time_embed_dim // 2),
            nn.SiLU(),
            nn.Linear(time_embed_dim // 2, time_embed_dim),
            nn.SiLU()
        )
        
        # 条件嵌入
        self.condition_embed = nn.Sequential(
            nn.Linear(condition_dim, time_embed_dim),
            nn.SiLU(),
            nn.Linear(time_embed_dim, time_embed_dim)
        )
        
        # 编码器（下采样）
        self.encoder_layers = nn.ModuleList()
        in_dim = input_dim
        
        for hidden_dim in hidden_dims:
            self.encoder_layers.append(
                nn.Sequential(
                    nn.Linear(in_dim + time_embed_dim, hidden_dim),
                    nn.GroupNorm(8, hidden_dim),
                    nn.SiLU(),
                    nn.Dropout(dropout),
                    nn.Linear(hidden_dim, hidden_dim),
                    nn.GroupNorm(8, hidden_dim),
                    nn.SiLU(),
                    nn.Dropout(dropout)
                )
            )
            in_dim = hidden_dim
        
        # 中间层
        self.middle = nn.Sequential(
            nn.Linear(hidden_dims[-1] + time_embed_dim, hidden_dims[-1]),
            nn.GroupNorm(8, hidden_dims[-1]),
            nn.SiLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dims[-1], hidden_dims[-1]),
            nn.GroupNorm(8, hidden_dims[-1]),
            nn.SiLU(),
            nn.Dropout(dropout)
        )
        
        # 解码器（上采样）
        self.decoder_layers = nn.ModuleList()
        
        for i, hidden_dim in enumerate(reversed(hidden_dims)):
            if i == 0:
                in_dim = hidden_dims[-1] + hidden_dims[-1]  # skip connection
            else:
                in_dim = hidden_dims[-(i+1)] + hidden_dims[-(i)]  # skip connection
            
            self.decoder_layers.append(
                nn.Sequential(
                    nn.Linear(in_dim + time_embed_dim, hidden_dim),
                    nn.GroupNorm(8, hidden_dim),
                    nn.SiLU(),
                    nn.Dropout(dropout),
                    nn.Linear(hidden_dim, hidden_dim),
                    nn.GroupNorm(8, hidden_dim),
                    nn.SiLU(),
                    nn.Dropout(dropout)
                )
            )
        
        # 输出层
        self.output = nn.Linear(hidden_dims[0], input_dim)
        
        # 初始化参数
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor, t: torch.Tensor, 
                condition: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入数据 [batch_size, input_dim]
            t: 时间步 [batch_size]
            condition: 条件（性能需求）[batch_size, condition_dim]
            
        Returns:
            预测的噪声 [batch_size, input_dim]
        """
        # 时间嵌入
        t_embed = self.time_embed(t.float().unsqueeze(-1))
        
        # 条件嵌入
        c_embed = self.condition_embed(condition)
        
        # 组合时间和条件嵌入
        embed = t_embed + c_embed
        
        # 编码器
        encoder_outputs = []
        h = x
        
        for layer in self.encoder_layers:
            h_input = torch.cat([h, embed], dim=-1)
            h = layer(h_input)
            encoder_outputs.append(h)
        
        # 中间层
        h_input = torch.cat([h, embed], dim=-1)
        h = self.middle(h_input)
        
        # 解码器
        for i, layer in enumerate(self.decoder_layers):
            # Skip connection
            skip = encoder_outputs[-(i+1)]
            h = torch.cat([h, skip], dim=-1)
            
            h_input = torch.cat([h, embed], dim=-1)
            h = layer(h_input)
        
        # 输出
        return self.output(h)

class ConditionalDDPM(nn.Module):
    """条件扩散概率模型"""
    
    def __init__(self, input_dim: int, condition_dim: int,
                 num_timesteps: int = 1000,
                 noise_schedule: str = 'linear',
                 unet_config: Optional[Dict[str, Any]] = None):
        """
        初始化条件DDPM
        
        Args:
            input_dim: 输入维度
            condition_dim: 条件维度
            num_timesteps: 扩散步数
            noise_schedule: 噪声调度类型
            unet_config: U-Net配置
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.condition_dim = condition_dim
        self.num_timesteps = num_timesteps
        
        # 噪声调度器
        self.noise_scheduler = NoiseScheduler(
            num_timesteps=num_timesteps,
            schedule_type=noise_schedule
        )
        
        # U-Net去噪网络
        default_unet_config = {
            'hidden_dims': [128, 256, 512],
            'time_embed_dim': 128,
            'dropout': 0.1
        }
        if unet_config:
            default_unet_config.update(unet_config)
        
        self.unet = ConditionalUNet(
            input_dim=input_dim,
            condition_dim=condition_dim,
            **default_unet_config
        )
        
        print(f"条件DDPM初始化完成:")
        print(f"  输入维度: {input_dim}")
        print(f"  条件维度: {condition_dim}")
        print(f"  扩散步数: {num_timesteps}")
        print(f"  噪声调度: {noise_schedule}")
        print(f"  U-Net参数数量: {sum(p.numel() for p in self.unet.parameters()):,}")
    
    def forward(self, x: torch.Tensor, condition: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        训练时的前向传播
        
        Args:
            x: 输入数据 [batch_size, input_dim]
            condition: 条件 [batch_size, condition_dim]
            
        Returns:
            损失相关信息
        """
        batch_size = x.shape[0]
        device = x.device
        
        # 随机采样时间步
        t = torch.randint(0, self.num_timesteps, (batch_size,), device=device)
        
        # 生成噪声
        noise = torch.randn_like(x)
        
        # 前向扩散
        x_t = self.noise_scheduler.q_sample(x, t, noise)
        
        # 预测噪声
        predicted_noise = self.unet(x_t, t, condition)
        
        # 计算损失
        loss = F.mse_loss(predicted_noise, noise)
        
        return {
            'loss': loss,
            'predicted_noise': predicted_noise,
            'target_noise': noise,
            'x_t': x_t,
            't': t
        }
    
    @torch.no_grad()
    def sample(self, condition: torch.Tensor, 
               num_samples: Optional[int] = None,
               return_intermediates: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        条件采样生成
        
        Args:
            condition: 条件 [batch_size, condition_dim] 或 [condition_dim]
            num_samples: 采样数量（如果condition是1D）
            return_intermediates: 是否返回中间步骤
            
        Returns:
            生成的样本，可选中间步骤
        """
        if condition.dim() == 1:
            if num_samples is None:
                num_samples = 1
            condition = condition.unsqueeze(0).repeat(num_samples, 1)
        
        batch_size = condition.shape[0]
        device = condition.device
        
        # 从纯噪声开始
        x = torch.randn(batch_size, self.input_dim, device=device)
        
        intermediates = []
        
        # 反向扩散过程
        for i in reversed(range(self.num_timesteps)):
            t = torch.full((batch_size,), i, device=device, dtype=torch.long)
            
            # 预测噪声
            predicted_noise = self.unet(x, t, condition)
            
            # 去噪一步
            x = self.noise_scheduler.p_sample(predicted_noise, x, t)
            
            if return_intermediates:
                intermediates.append(x.clone())
        
        if return_intermediates:
            return x, intermediates
        else:
            return x
    
    @torch.no_grad()
    def ddim_sample(self, condition: torch.Tensor, 
                    num_inference_steps: int = 50,
                    eta: float = 0.0,
                    num_samples: Optional[int] = None) -> torch.Tensor:
        """
        DDIM采样（更快的采样方法）
        
        Args:
            condition: 条件
            num_inference_steps: 推理步数
            eta: DDIM参数（0为确定性采样）
            num_samples: 采样数量
            
        Returns:
            生成的样本
        """
        if condition.dim() == 1:
            if num_samples is None:
                num_samples = 1
            condition = condition.unsqueeze(0).repeat(num_samples, 1)
        
        batch_size = condition.shape[0]
        device = condition.device
        
        # 创建时间步序列
        timesteps = torch.linspace(self.num_timesteps - 1, 0, num_inference_steps, dtype=torch.long, device=device)
        
        # 从纯噪声开始
        x = torch.randn(batch_size, self.input_dim, device=device)
        
        for i, t in enumerate(timesteps):
            t_batch = t.repeat(batch_size)
            
            # 预测噪声
            predicted_noise = self.unet(x, t_batch, condition)
            
            # DDIM更新
            alpha_t = self.noise_scheduler.alphas_cumprod[t]
            
            if i < len(timesteps) - 1:
                alpha_t_prev = self.noise_scheduler.alphas_cumprod[timesteps[i + 1]]
            else:
                alpha_t_prev = torch.tensor(1.0, device=device)
            
            # 预测x_0
            pred_x0 = (x - torch.sqrt(1 - alpha_t) * predicted_noise) / torch.sqrt(alpha_t)
            
            # DDIM更新公式
            sigma_t = eta * torch.sqrt((1 - alpha_t_prev) / (1 - alpha_t)) * torch.sqrt(1 - alpha_t / alpha_t_prev)
            
            noise = torch.randn_like(x) if i < len(timesteps) - 1 else torch.zeros_like(x)
            
            x = torch.sqrt(alpha_t_prev) * pred_x0 + torch.sqrt(1 - alpha_t_prev - sigma_t**2) * predicted_noise + sigma_t * noise
        
        return x

class GraphDiffusionModel(nn.Module):
    """图扩散模型：专门用于生成图结构（节点坐标和边连接）"""

    def __init__(self, max_nodes: int = 64, node_dim: int = 3,
                 condition_dim: int = 18, num_timesteps: int = 1000,
                 coordinate_diffusion_config: Optional[Dict[str, Any]] = None,
                 edge_prediction_config: Optional[Dict[str, Any]] = None):
        """
        初始化图扩散模型

        Args:
            max_nodes: 最大节点数
            node_dim: 节点特征维度（坐标维度）
            condition_dim: 条件维度（性能特征）
            num_timesteps: 扩散步数
            coordinate_diffusion_config: 坐标扩散配置
            edge_prediction_config: 边预测配置
        """
        super().__init__()

        self.max_nodes = max_nodes
        self.node_dim = node_dim
        self.condition_dim = condition_dim
        self.num_timesteps = num_timesteps

        # 坐标扩散模型（连续值）
        coord_input_dim = max_nodes * node_dim  # 展平的坐标

        default_coord_config = {
            'unet_config': {
                'hidden_dims': [256, 512, 1024],
                'time_embed_dim': 256,
                'dropout': 0.1
            },
            'noise_schedule': 'cosine'
        }
        if coordinate_diffusion_config:
            default_coord_config.update(coordinate_diffusion_config)

        self.coordinate_diffusion = ConditionalDDPM(
            input_dim=coord_input_dim,
            condition_dim=condition_dim,
            num_timesteps=num_timesteps,
            **default_coord_config
        )

        # 边预测网络（离散值）
        default_edge_config = {
            'hidden_dims': [256, 512, 256],
            'dropout': 0.1,
            'use_attention': True
        }
        if edge_prediction_config:
            default_edge_config.update(edge_prediction_config)

        self.edge_predictor = EdgePredictor(
            node_dim=node_dim,
            condition_dim=condition_dim,
            max_nodes=max_nodes,
            **default_edge_config
        )

        print(f"图扩散模型初始化完成:")
        print(f"  最大节点数: {max_nodes}")
        print(f"  节点维度: {node_dim}")
        print(f"  条件维度: {condition_dim}")
        print(f"  坐标扩散参数: {sum(p.numel() for p in self.coordinate_diffusion.parameters()):,}")
        print(f"  边预测参数: {sum(p.numel() for p in self.edge_predictor.parameters()):,}")

    def forward(self, node_coords: torch.Tensor, edge_index: torch.Tensor,
                condition: torch.Tensor, node_mask: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        训练时的前向传播

        Args:
            node_coords: 节点坐标 [batch_size, max_nodes, node_dim]
            edge_index: 边索引 [batch_size, 2, max_edges]
            condition: 条件 [batch_size, condition_dim]
            node_mask: 节点mask [batch_size, max_nodes]

        Returns:
            损失信息
        """
        batch_size = node_coords.shape[0]

        # 1. 坐标扩散损失
        # 展平坐标并应用mask
        coords_flat = node_coords.view(batch_size, -1)  # [batch_size, max_nodes * node_dim]

        # 应用节点mask到坐标
        node_mask_expanded = node_mask.unsqueeze(-1).expand(-1, -1, self.node_dim)
        masked_coords = node_coords * node_mask_expanded
        coords_flat_masked = masked_coords.view(batch_size, -1)

        coord_loss_info = self.coordinate_diffusion(coords_flat_masked, condition)

        # 2. 边预测损失
        edge_loss_info = self.edge_predictor(node_coords, edge_index, condition, node_mask)

        # 3. 组合损失
        total_loss = coord_loss_info['loss'] + edge_loss_info['loss']

        return {
            'total_loss': total_loss,
            'coordinate_loss': coord_loss_info['loss'],
            'edge_loss': edge_loss_info['loss'],
            'coord_loss_info': coord_loss_info,
            'edge_loss_info': edge_loss_info
        }

    @torch.no_grad()
    def generate(self, condition: torch.Tensor,
                 num_nodes: Optional[torch.Tensor] = None,
                 num_samples: Optional[int] = None,
                 use_ddim: bool = True,
                 ddim_steps: int = 50) -> Dict[str, torch.Tensor]:
        """
        生成图结构

        Args:
            condition: 条件 [batch_size, condition_dim] 或 [condition_dim]
            num_nodes: 每个图的节点数 [batch_size] 或标量
            num_samples: 采样数量
            use_ddim: 是否使用DDIM采样
            ddim_steps: DDIM步数

        Returns:
            生成的图结构
        """
        if condition.dim() == 1:
            if num_samples is None:
                num_samples = 1
            condition = condition.unsqueeze(0).repeat(num_samples, 1)

        batch_size = condition.shape[0]
        device = condition.device

        # 处理节点数
        if num_nodes is None:
            # 随机选择节点数（8-36范围）
            num_nodes = torch.randint(8, 37, (batch_size,), device=device)
        elif isinstance(num_nodes, int):
            num_nodes = torch.full((batch_size,), num_nodes, device=device)

        # 创建节点mask
        node_mask = torch.zeros(batch_size, self.max_nodes, dtype=torch.bool, device=device)
        for i, n in enumerate(num_nodes):
            node_mask[i, :n] = True

        # 1. 生成坐标
        if use_ddim:
            coords_flat = self.coordinate_diffusion.ddim_sample(
                condition, num_inference_steps=ddim_steps
            )
        else:
            coords_flat = self.coordinate_diffusion.sample(condition)

        # 重塑坐标
        node_coords = coords_flat.view(batch_size, self.max_nodes, self.node_dim)

        # 应用节点mask
        node_mask_expanded = node_mask.unsqueeze(-1).expand(-1, -1, self.node_dim)
        node_coords = node_coords * node_mask_expanded

        # 2. 生成边
        edge_probs = self.edge_predictor.predict_edges(node_coords, condition, node_mask)
        edge_index = self.edge_predictor.sample_edges(edge_probs, node_mask)

        return {
            'node_coords': node_coords,
            'edge_index': edge_index,
            'node_mask': node_mask,
            'num_nodes': num_nodes,
            'edge_probs': edge_probs
        }

class EdgePredictor(nn.Module):
    """边预测器：预测图中的边连接"""

    def __init__(self, node_dim: int, condition_dim: int, max_nodes: int,
                 hidden_dims: List[int] = [256, 512, 256],
                 dropout: float = 0.1, use_attention: bool = True):
        """
        初始化边预测器

        Args:
            node_dim: 节点特征维度
            condition_dim: 条件维度
            max_nodes: 最大节点数
            hidden_dims: 隐藏层维度
            dropout: Dropout概率
            use_attention: 是否使用注意力机制
        """
        super().__init__()

        self.node_dim = node_dim
        self.condition_dim = condition_dim
        self.max_nodes = max_nodes
        self.use_attention = use_attention

        # 节点编码器
        self.node_encoder = nn.Sequential(
            nn.Linear(node_dim, hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dims[0], hidden_dims[0])
        )

        # 条件编码器
        self.condition_encoder = nn.Sequential(
            nn.Linear(condition_dim, hidden_dims[0]),
            nn.ReLU(),
            nn.Linear(hidden_dims[0], hidden_dims[0])
        )

        # 边预测网络
        edge_input_dim = hidden_dims[0] * 2 + hidden_dims[0]  # 两个节点 + 条件

        self.edge_predictor = nn.Sequential(
            nn.Linear(edge_input_dim, hidden_dims[1]),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dims[1], hidden_dims[2]),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dims[2], 1),
            nn.Sigmoid()
        )

        # 注意力机制（可选）
        if use_attention:
            self.attention = nn.MultiheadAttention(
                embed_dim=hidden_dims[0],
                num_heads=8,
                dropout=dropout,
                batch_first=True
            )

    def forward(self, node_coords: torch.Tensor, edge_index: torch.Tensor,
                condition: torch.Tensor, node_mask: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        训练时的前向传播

        Args:
            node_coords: 节点坐标 [batch_size, max_nodes, node_dim]
            edge_index: 真实边索引 [batch_size, 2, max_edges]
            condition: 条件 [batch_size, condition_dim]
            node_mask: 节点mask [batch_size, max_nodes]

        Returns:
            损失信息
        """
        batch_size = node_coords.shape[0]
        device = node_coords.device

        # 预测边概率
        edge_probs = self.predict_edges(node_coords, condition, node_mask)

        # 构建目标边矩阵
        target_edges = torch.zeros(batch_size, self.max_nodes, self.max_nodes, device=device)

        for b in range(batch_size):
            valid_edges = edge_index[b]
            # 过滤有效边（在节点mask范围内）
            valid_mask = (valid_edges[0] < node_mask[b].sum()) & (valid_edges[1] < node_mask[b].sum())
            valid_edges = valid_edges[:, valid_mask]

            if valid_edges.shape[1] > 0:
                target_edges[b, valid_edges[0], valid_edges[1]] = 1.0
                target_edges[b, valid_edges[1], valid_edges[0]] = 1.0  # 无向图

        # 应用节点mask到目标
        node_mask_2d = node_mask.unsqueeze(-1) & node_mask.unsqueeze(-2)
        target_edges = target_edges * node_mask_2d.float()
        edge_probs = edge_probs * node_mask_2d.float()

        # 计算二元交叉熵损失
        loss = F.binary_cross_entropy(edge_probs, target_edges, reduction='none')
        loss = loss * node_mask_2d.float()  # 只计算有效位置的损失
        loss = loss.sum() / node_mask_2d.float().sum()  # 平均损失

        return {
            'loss': loss,
            'edge_probs': edge_probs,
            'target_edges': target_edges
        }

    def predict_edges(self, node_coords: torch.Tensor, condition: torch.Tensor,
                     node_mask: torch.Tensor) -> torch.Tensor:
        """
        预测边概率矩阵

        Args:
            node_coords: 节点坐标 [batch_size, max_nodes, node_dim]
            condition: 条件 [batch_size, condition_dim]
            node_mask: 节点mask [batch_size, max_nodes]

        Returns:
            边概率矩阵 [batch_size, max_nodes, max_nodes]
        """
        batch_size = node_coords.shape[0]

        # 编码节点
        node_features = self.node_encoder(node_coords)  # [batch_size, max_nodes, hidden_dim]

        # 编码条件
        condition_features = self.condition_encoder(condition)  # [batch_size, hidden_dim]
        condition_features = condition_features.unsqueeze(1).expand(-1, self.max_nodes, -1)

        # 注意力机制（可选）
        if self.use_attention:
            # 应用mask到注意力
            attn_mask = ~node_mask  # 注意力mask（True表示忽略）
            node_features, _ = self.attention(
                node_features, node_features, node_features,
                key_padding_mask=attn_mask
            )

        # 组合节点和条件特征
        enhanced_features = node_features + condition_features

        # 计算所有节点对的边概率
        edge_probs = torch.zeros(batch_size, self.max_nodes, self.max_nodes, device=node_coords.device)

        for i in range(self.max_nodes):
            for j in range(i + 1, self.max_nodes):  # 只计算上三角，避免重复
                # 节点对特征
                node_i = enhanced_features[:, i]  # [batch_size, hidden_dim]
                node_j = enhanced_features[:, j]  # [batch_size, hidden_dim]
                condition_ij = condition_features[:, 0]  # [batch_size, hidden_dim]

                # 拼接特征
                edge_input = torch.cat([node_i, node_j, condition_ij], dim=-1)

                # 预测边概率
                prob = self.edge_predictor(edge_input).squeeze(-1)  # [batch_size]

                edge_probs[:, i, j] = prob
                edge_probs[:, j, i] = prob  # 对称（无向图）

        return edge_probs

    def sample_edges(self, edge_probs: torch.Tensor, node_mask: torch.Tensor,
                    threshold: float = 0.5) -> torch.Tensor:
        """
        从边概率矩阵采样边

        Args:
            edge_probs: 边概率矩阵 [batch_size, max_nodes, max_nodes]
            node_mask: 节点mask [batch_size, max_nodes]
            threshold: 采样阈值

        Returns:
            边索引 [batch_size, 2, num_edges]
        """
        batch_size = edge_probs.shape[0]
        device = edge_probs.device

        edge_indices = []

        for b in range(batch_size):
            num_nodes = node_mask[b].sum().item()
            probs = edge_probs[b, :num_nodes, :num_nodes]

            # 采样边（只考虑上三角避免重复）
            edges = []
            for i in range(num_nodes):
                for j in range(i + 1, num_nodes):
                    if torch.rand(1, device=device) < probs[i, j]:
                        edges.append([i, j])

            if edges:
                edges = torch.tensor(edges, device=device).T  # [2, num_edges]
            else:
                # 如果没有边，创建空的边张量
                edges = torch.empty(2, 0, dtype=torch.long, device=device)

            edge_indices.append(edges)

        # 填充到相同长度
        max_edges = max(edges.shape[1] for edges in edge_indices)
        if max_edges == 0:
            max_edges = 1  # 至少保留一个位置

        padded_edges = torch.zeros(batch_size, 2, max_edges, dtype=torch.long, device=device)

        for b, edges in enumerate(edge_indices):
            if edges.shape[1] > 0:
                padded_edges[b, :, :edges.shape[1]] = edges

        return padded_edges
