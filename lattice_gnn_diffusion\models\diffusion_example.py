"""
扩散模型使用示例：演示如何使用条件扩散模型进行图结构生成
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import os

# 导入扩散模型组件
try:
    from .diffusion_core import GraphDiffusionModel
    from .conditioning import PerformanceConditioner, AdaptiveConditioner
    from ..data.preprocessing import LatticeDataPreprocessor
    from ..utils.graph_utils import visualize_graph_3d
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from models.diffusion_core import GraphDiffusionModel
    from models.conditioning import PerformanceConditioner, AdaptiveConditioner
    from data.preprocessing import LatticeDataPreprocessor
    from utils.graph_utils import visualize_graph_3d

class DiffusionTrainer:
    """扩散模型训练器"""
    
    def __init__(self, model: GraphDiffusionModel, 
                 optimizer: torch.optim.Optimizer,
                 device: str = 'cpu'):
        """
        初始化训练器
        
        Args:
            model: 图扩散模型
            optimizer: 优化器
            device: 设备
        """
        self.model = model.to(device)
        self.optimizer = optimizer
        self.device = device
        self.training_history = []
    
    def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        单步训练
        
        Args:
            batch: 批次数据
            
        Returns:
            损失信息
        """
        self.model.train()
        self.optimizer.zero_grad()
        
        # 移动数据到设备
        node_coords = batch['node_coords'].to(self.device)
        edge_index = batch['edge_index'].to(self.device)
        condition = batch['performance'].to(self.device)
        node_mask = batch['node_mask'].to(self.device)
        
        # 前向传播
        loss_info = self.model(node_coords, edge_index, condition, node_mask)
        
        # 反向传播
        total_loss = loss_info['total_loss']
        total_loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
        
        # 优化器步骤
        self.optimizer.step()
        
        # 返回损失信息
        return {
            'total_loss': total_loss.item(),
            'coordinate_loss': loss_info['coordinate_loss'].item(),
            'edge_loss': loss_info['edge_loss'].item()
        }
    
    def train_epoch(self, dataloader, epoch: int) -> Dict[str, float]:
        """
        训练一个epoch
        
        Args:
            dataloader: 数据加载器
            epoch: 当前epoch
            
        Returns:
            平均损失
        """
        total_losses = {'total_loss': 0, 'coordinate_loss': 0, 'edge_loss': 0}
        num_batches = 0
        
        for batch_idx, batch in enumerate(dataloader):
            losses = self.train_step(batch)
            
            for key in total_losses:
                total_losses[key] += losses[key]
            num_batches += 1
            
            if batch_idx % 10 == 0:
                print(f"Epoch {epoch}, Batch {batch_idx}: "
                      f"Total Loss = {losses['total_loss']:.4f}, "
                      f"Coord Loss = {losses['coordinate_loss']:.4f}, "
                      f"Edge Loss = {losses['edge_loss']:.4f}")
        
        # 计算平均损失
        avg_losses = {key: value / num_batches for key, value in total_losses.items()}
        self.training_history.append(avg_losses)
        
        return avg_losses
    
    def generate_samples(self, conditions: torch.Tensor, 
                        num_nodes: Optional[torch.Tensor] = None,
                        num_samples_per_condition: int = 1) -> Dict[str, torch.Tensor]:
        """
        生成样本
        
        Args:
            conditions: 条件 [num_conditions, condition_dim]
            num_nodes: 节点数 [num_conditions]
            num_samples_per_condition: 每个条件生成的样本数
            
        Returns:
            生成的样本
        """
        self.model.eval()
        
        with torch.no_grad():
            # 扩展条件
            if num_samples_per_condition > 1:
                conditions = conditions.repeat_interleave(num_samples_per_condition, dim=0)
                if num_nodes is not None:
                    num_nodes = num_nodes.repeat_interleave(num_samples_per_condition, dim=0)
            
            conditions = conditions.to(self.device)
            if num_nodes is not None:
                num_nodes = num_nodes.to(self.device)
            
            # 生成
            generated = self.model.generate(
                condition=conditions,
                num_nodes=num_nodes,
                use_ddim=True,
                ddim_steps=50
            )
            
            # 移回CPU
            for key in generated:
                if isinstance(generated[key], torch.Tensor):
                    generated[key] = generated[key].cpu()
            
            return generated

def create_synthetic_data(num_samples: int = 100, max_nodes: int = 64) -> Dict[str, torch.Tensor]:
    """
    创建合成数据用于演示
    
    Args:
        num_samples: 样本数量
        max_nodes: 最大节点数
        
    Returns:
        合成数据
    """
    print(f"创建 {num_samples} 个合成样本...")
    
    data = {
        'node_coords': [],
        'edge_index': [],
        'performance': [],
        'node_mask': []
    }
    
    for i in range(num_samples):
        # 随机节点数（8-36）
        num_nodes = np.random.randint(8, 37)
        
        # 生成节点坐标
        coords = torch.randn(max_nodes, 3)
        coords[:num_nodes] = torch.rand(num_nodes, 3)  # 有效节点在[0,1]范围内
        
        # 生成边（简单的距离阈值）
        edges = []
        threshold = 0.3
        for j in range(num_nodes):
            for k in range(j + 1, num_nodes):
                dist = torch.norm(coords[j] - coords[k])
                if dist < threshold:
                    edges.append([j, k])
        
        # 填充边索引
        max_edges = 20
        edge_index = torch.zeros(2, max_edges, dtype=torch.long)
        if edges:
            edges_tensor = torch.tensor(edges[:max_edges]).T
            edge_index[:, :edges_tensor.shape[1]] = edges_tensor
        
        # 生成性能（基于节点数和边数的简单函数）
        performance = torch.tensor([
            num_nodes / 36.0,  # 归一化节点数
            len(edges) / 50.0,  # 归一化边数
            torch.rand(1).item(),  # 随机性能1
            torch.rand(1).item(),  # 随机性能2
            *torch.rand(14).tolist()  # 其他随机性能
        ], dtype=torch.float32)
        
        # 节点mask
        node_mask = torch.zeros(max_nodes, dtype=torch.bool)
        node_mask[:num_nodes] = True
        
        data['node_coords'].append(coords)
        data['edge_index'].append(edge_index)
        data['performance'].append(performance)
        data['node_mask'].append(node_mask)
    
    # 转换为张量
    for key in data:
        data[key] = torch.stack(data[key])
    
    print(f"合成数据创建完成:")
    print(f"  节点坐标形状: {data['node_coords'].shape}")
    print(f"  边索引形状: {data['edge_index'].shape}")
    print(f"  性能形状: {data['performance'].shape}")
    print(f"  节点mask形状: {data['node_mask'].shape}")
    
    return data

def demonstrate_diffusion_model():
    """演示扩散模型的使用"""
    print("扩散模型使用演示")
    print("=" * 50)
    
    # 设置参数
    max_nodes = 64
    node_dim = 3
    condition_dim = 18
    batch_size = 8
    num_epochs = 5
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    print(f"使用设备: {device}")
    
    # 1. 创建模型
    print("\n1. 创建图扩散模型...")
    model = GraphDiffusionModel(
        max_nodes=max_nodes,
        node_dim=node_dim,
        condition_dim=condition_dim,
        num_timesteps=100,
        coordinate_diffusion_config={
            'unet_config': {
                'hidden_dims': [128, 256, 128],
                'time_embed_dim': 128,
                'dropout': 0.1
            },
            'noise_schedule': 'cosine'
        },
        edge_prediction_config={
            'hidden_dims': [128, 256, 128],
            'dropout': 0.1,
            'use_attention': True
        }
    )
    
    # 2. 创建优化器
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-5)
    
    # 3. 创建训练器
    trainer = DiffusionTrainer(model, optimizer, device)
    
    # 4. 创建合成数据
    print("\n2. 创建合成训练数据...")
    train_data = create_synthetic_data(num_samples=80)
    val_data = create_synthetic_data(num_samples=20)
    
    # 创建数据加载器
    from torch.utils.data import TensorDataset, DataLoader
    
    train_dataset = TensorDataset(
        train_data['node_coords'],
        train_data['edge_index'],
        train_data['performance'],
        train_data['node_mask']
    )
    
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        drop_last=True
    )
    
    # 5. 训练模型
    print(f"\n3. 开始训练 {num_epochs} 个epochs...")
    for epoch in range(num_epochs):
        print(f"\nEpoch {epoch + 1}/{num_epochs}")
        print("-" * 30)
        
        # 准备批次数据格式
        epoch_losses = {'total_loss': 0, 'coordinate_loss': 0, 'edge_loss': 0}
        num_batches = 0
        
        for batch_idx, (node_coords, edge_index, performance, node_mask) in enumerate(train_loader):
            batch = {
                'node_coords': node_coords,
                'edge_index': edge_index,
                'performance': performance,
                'node_mask': node_mask
            }
            
            losses = trainer.train_step(batch)
            
            for key in epoch_losses:
                epoch_losses[key] += losses[key]
            num_batches += 1
            
            if batch_idx % 5 == 0:
                print(f"  Batch {batch_idx}: Total Loss = {losses['total_loss']:.4f}")
        
        # 计算平均损失
        avg_losses = {key: value / num_batches for key, value in epoch_losses.items()}
        print(f"Epoch {epoch + 1} 平均损失:")
        print(f"  Total: {avg_losses['total_loss']:.4f}")
        print(f"  Coordinate: {avg_losses['coordinate_loss']:.4f}")
        print(f"  Edge: {avg_losses['edge_loss']:.4f}")
    
    # 6. 生成样本
    print("\n4. 生成新的图结构...")
    
    # 使用验证数据的条件进行生成
    test_conditions = val_data['performance'][:5]  # 取前5个条件
    test_num_nodes = torch.tensor([12, 16, 20, 24, 28])  # 指定节点数
    
    generated = trainer.generate_samples(
        conditions=test_conditions,
        num_nodes=test_num_nodes,
        num_samples_per_condition=2
    )
    
    print("生成结果:")
    print(f"  生成的节点坐标形状: {generated['node_coords'].shape}")
    print(f"  生成的边索引形状: {generated['edge_index'].shape}")
    print(f"  节点mask形状: {generated['node_mask'].shape}")
    
    # 7. 分析生成质量
    print("\n5. 分析生成质量...")
    
    for i in range(min(3, generated['node_coords'].shape[0])):
        num_nodes = generated['num_nodes'][i].item()
        coords = generated['node_coords'][i, :num_nodes]
        
        # 计算基本统计
        coord_mean = coords.mean(dim=0)
        coord_std = coords.std(dim=0)
        
        print(f"样本 {i + 1}:")
        print(f"  节点数: {num_nodes}")
        print(f"  坐标均值: [{coord_mean[0]:.3f}, {coord_mean[1]:.3f}, {coord_mean[2]:.3f}]")
        print(f"  坐标标准差: [{coord_std[0]:.3f}, {coord_std[1]:.3f}, {coord_std[2]:.3f}]")
        
        # 检查坐标范围
        coord_min = coords.min(dim=0)[0]
        coord_max = coords.max(dim=0)[0]
        print(f"  坐标范围: [{coord_min[0]:.3f}, {coord_max[0]:.3f}] x "
              f"[{coord_min[1]:.3f}, {coord_max[1]:.3f}] x "
              f"[{coord_min[2]:.3f}, {coord_max[2]:.3f}]")
    
    print("\n✅ 扩散模型演示完成！")
    
    return model, trainer, generated

def demonstrate_conditioning():
    """演示条件编码的使用"""
    print("\n条件编码演示")
    print("=" * 30)
    
    # 1. 基础性能条件编码器
    print("1. 基础性能条件编码器...")
    
    performance_dim = 18
    condition_dim = 128
    batch_size = 4
    
    conditioner = PerformanceConditioner(
        performance_dim=performance_dim,
        condition_dim=condition_dim,
        encoding_type='mlp'
    )
    
    # 测试不同的性能输入
    performance_data = [
        torch.rand(batch_size, performance_dim),  # 随机性能
        torch.ones(batch_size, performance_dim) * 0.5,  # 中等性能
        torch.zeros(batch_size, performance_dim),  # 最低性能
        torch.ones(batch_size, performance_dim),  # 最高性能
    ]
    
    for i, perf in enumerate(performance_data):
        condition = conditioner(perf)
        print(f"  性能输入 {i + 1}: {perf[0, :3].tolist()[:3]}... -> 条件维度: {condition.shape}")
    
    # 2. 自适应条件编码器
    print("\n2. 自适应条件编码器...")
    
    adaptive_conditioner = AdaptiveConditioner(
        max_performance_dim=30,
        condition_dim=condition_dim
    )
    
    # 测试不同维度的性能输入
    for dim in [10, 15, 18, 25]:
        perf = torch.rand(batch_size, dim)
        condition = adaptive_conditioner(perf)
        print(f"  性能维度 {dim} -> 条件维度: {condition.shape}")
    
    print("✅ 条件编码演示完成！")

if __name__ == "__main__":
    # 运行演示
    try:
        # 基础扩散模型演示
        model, trainer, generated = demonstrate_diffusion_model()
        
        # 条件编码演示
        demonstrate_conditioning()
        
        print("\n🎉 所有演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
