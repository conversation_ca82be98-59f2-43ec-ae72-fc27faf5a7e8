"""
GAT编码器使用示例和集成指南
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 示例代码（需要安装PyTorch后运行）
EXAMPLE_CODE = '''
import torch
from torch_geometric.data import Data, Batch
from models.graph_encoder import GATEncoder, MultiScaleGATEncoder
from data.preprocessing import LatticeDataPreprocessor
from data.dataset import LatticeDataset

def create_gat_encoder():
    """创建GAT编码器实例"""
    encoder = GATEncoder(
        input_dim=3,           # 输入特征维度（x,y,z坐标）
        hidden_dim=128,        # 隐藏层维度
        output_dim=256,        # 输出图嵌入维度
        num_layers=4,          # GAT层数
        num_heads=8,           # 注意力头数
        dropout=0.1,           # Dropout概率
        use_residual=True,     # 使用残差连接
        use_norm=True,         # 使用图归一化
        pooling_method='attention',  # 池化方法
        use_pos_encoding=False # 位置编码
    )
    return encoder

def encode_single_graph():
    """编码单个图的示例"""
    # 创建编码器
    encoder = create_gat_encoder()
    
    # 创建示例图数据
    num_nodes = 20
    x = torch.randn(num_nodes, 3)  # 节点坐标
    edge_index = torch.randint(0, num_nodes, (2, 40))  # 边索引
    
    # 创建节点mask（模拟变长图）
    valid_nodes = 15
    node_mask = torch.zeros(num_nodes, dtype=torch.bool)
    node_mask[:valid_nodes] = True
    
    # 前向传播
    with torch.no_grad():
        graph_embedding = encoder(x, edge_index, node_mask=node_mask)
    
    print(f"输入图: {num_nodes}节点, {edge_index.shape[1]}边")
    print(f"有效节点: {valid_nodes}")
    print(f"图嵌入形状: {graph_embedding.shape}")
    
    return graph_embedding

def encode_batch_graphs():
    """批处理编码示例"""
    encoder = create_gat_encoder()
    
    # 创建多个图
    graphs = []
    for i in range(4):
        num_nodes = torch.randint(10, 25, (1,)).item()
        x = torch.randn(num_nodes, 3)
        edge_index = torch.randint(0, num_nodes, (2, num_nodes * 2))
        
        # 节点mask
        valid_nodes = torch.randint(8, num_nodes + 1, (1,)).item()
        node_mask = torch.zeros(num_nodes, dtype=torch.bool)
        node_mask[:valid_nodes] = True
        
        data = Data(x=x, edge_index=edge_index, node_mask=node_mask)
        graphs.append(data)
    
    # 创建批处理
    batch = Batch.from_data_list(graphs)
    
    # 批处理编码
    with torch.no_grad():
        batch_embeddings = encoder(
            batch.x, 
            batch.edge_index, 
            batch=batch.batch,
            node_mask=batch.node_mask
        )
    
    print(f"批处理: {len(graphs)}个图")
    print(f"总节点数: {batch.x.shape[0]}")
    print(f"总边数: {batch.edge_index.shape[1]}")
    print(f"批嵌入形状: {batch_embeddings.shape}")
    
    return batch_embeddings

def integrate_with_data_pipeline():
    """与数据管道集成示例"""
    # 数据预处理
    preprocessor = LatticeDataPreprocessor(
        node_file='data/node.csv',
        edge_file='data/edge.csv', 
        lattice_file='data/lattice.csv',
        max_nodes=64
    )
    
    # 加载和预处理数据
    preprocessor.load_data()
    preprocessor.fit_scalers()
    
    # 创建数据集
    dataset = LatticeDataset(
        preprocessor=preprocessor,
        cache_dir='data/cache'
    )
    
    # 创建编码器
    encoder = create_gat_encoder()
    
    # 编码第一个样本
    data = dataset[0]
    with torch.no_grad():
        embedding = encoder(
            data.x, 
            data.edge_index,
            node_mask=data.node_mask
        )
    
    print(f"真实数据编码:")
    print(f"点阵名称: {data.lattice_name}")
    print(f"节点数: {data.node_mask.sum().item()}")
    print(f"图嵌入: {embedding.shape}")
    print(f"性能标签: {data.y.shape}")
    
    return embedding

def multiscale_encoding_example():
    """多尺度编码示例"""
    # 创建多尺度编码器
    encoder = MultiScaleGATEncoder(
        input_dim=3,
        hidden_dims=[64, 128, 256],  # 多个尺度
        output_dim=512,
        num_heads=8,
        dropout=0.1
    )
    
    # 测试数据
    num_nodes = 20
    x = torch.randn(num_nodes, 3)
    edge_index = torch.randint(0, num_nodes, (2, 40))
    node_mask = torch.ones(num_nodes, dtype=torch.bool)
    
    # 编码
    with torch.no_grad():
        embedding = encoder(x, edge_index, node_mask=node_mask)
    
    print(f"多尺度编码:")
    print(f"输入: {x.shape}")
    print(f"输出: {embedding.shape}")
    
    return embedding

def attention_analysis_example():
    """注意力分析示例"""
    encoder = create_gat_encoder()
    
    # 测试数据
    num_nodes = 15
    x = torch.randn(num_nodes, 3)
    edge_index = torch.randint(0, num_nodes, (2, 30))
    node_mask = torch.ones(num_nodes, dtype=torch.bool)
    
    # 获取注意力权重
    with torch.no_grad():
        attn_weights = encoder.get_attention_weights(
            x, edge_index, node_mask=node_mask, layer_idx=-1
        )
    
    print(f"注意力分析:")
    print(f"注意力权重: {attn_weights}")
    
    return attn_weights

if __name__ == "__main__":
    print("GAT编码器使用示例")
    print("=" * 50)
    
    try:
        # 单图编码
        print("\\n1. 单图编码示例:")
        encode_single_graph()
        
        # 批处理编码
        print("\\n2. 批处理编码示例:")
        encode_batch_graphs()
        
        # 多尺度编码
        print("\\n3. 多尺度编码示例:")
        multiscale_encoding_example()
        
        # 数据管道集成（需要真实数据文件）
        print("\\n4. 数据管道集成示例:")
        try:
            integrate_with_data_pipeline()
        except FileNotFoundError:
            print("跳过（需要真实数据文件）")
        
        # 注意力分析
        print("\\n5. 注意力分析示例:")
        attention_analysis_example()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请安装依赖: pip install torch torch-geometric")
'''

def print_integration_guide():
    """打印集成指南"""
    print("🔧 GAT编码器集成指南")
    print("=" * 50)
    
    print("\n📦 1. 依赖安装:")
    print("pip install torch torch-geometric numpy")
    
    print("\n🏗️ 2. 基本使用:")
    print("from models.graph_encoder import GATEncoder")
    print("encoder = GATEncoder(input_dim=3, hidden_dim=128, output_dim=256)")
    print("embedding = encoder(x, edge_index, node_mask=node_mask)")
    
    print("\n⚙️ 3. 配置参数:")
    print("• input_dim: 输入特征维度（通常为3，表示x,y,z坐标）")
    print("• hidden_dim: 隐藏层维度（推荐128-256）")
    print("• output_dim: 输出图嵌入维度（推荐256-512）")
    print("• num_layers: GAT层数（推荐3-5层）")
    print("• num_heads: 注意力头数（推荐4-8头）")
    print("• dropout: Dropout概率（推荐0.1-0.2）")
    print("• pooling_method: 池化方法（'attention', 'mean', 'max', 'add'）")
    
    print("\n🎯 4. 变长图处理:")
    print("• 使用node_mask参数标识有效节点")
    print("• 支持padding到固定大小（如64节点）")
    print("• 自动处理mask在注意力计算中的应用")
    
    print("\n📊 5. 批处理支持:")
    print("• 使用torch_geometric.data.Batch创建批处理")
    print("• 传入batch参数进行批处理编码")
    print("• 自动处理不同大小图的批处理")
    
    print("\n🔍 6. 注意力可视化:")
    print("• 使用get_attention_weights()获取注意力权重")
    print("• 支持指定层的注意力分析")
    print("• 可用于模型解释性分析")
    
    print("\n🚀 7. 性能优化:")
    print("• 使用残差连接防止梯度消失")
    print("• 图归一化提升训练稳定性")
    print("• 支持位置编码增强表示能力")
    print("• 多尺度编码器捕获不同层次特征")

def print_architecture_details():
    """打印架构详细信息"""
    print("\n🏛️ GAT编码器架构详解")
    print("=" * 50)
    
    print("\n📋 核心组件:")
    print("1. MultiHeadGATLayer - 多头图注意力层")
    print("   • 实现多头注意力机制")
    print("   • 支持变长图的mask处理")
    print("   • 包含消息传递和注意力计算")
    
    print("\n2. GraphNorm - 图归一化层")
    print("   • 对图中节点特征进行归一化")
    print("   • 支持批处理和mask处理")
    print("   • 提升训练稳定性")
    
    print("\n3. ResidualBlock - 残差连接块")
    print("   • 包装任意层添加残差连接")
    print("   • 防止深层网络梯度消失")
    print("   • 包含Dropout正则化")
    
    print("\n4. AttentionPooling - 注意力池化")
    print("   • 将节点级特征聚合为图级特征")
    print("   • 学习重要节点的权重")
    print("   • 支持mask处理")
    
    print("\n5. PositionalEncoding - 位置编码")
    print("   • 为节点添加位置信息")
    print("   • 增强图结构表示能力")
    print("   • 可选组件")
    
    print("\n🔄 数据流:")
    print("输入坐标 → 线性投影 → [位置编码] → GAT层×N → 池化 → 输出嵌入")
    print("每个GAT层: 注意力计算 → 消息传递 → [残差连接] → [归一化] → 激活")

def main():
    """主函数"""
    print("GAT编码器使用示例和集成指南")
    print("=" * 60)
    
    print_integration_guide()
    print_architecture_details()
    
    print(f"\n📝 示例代码:")
    print("以下代码需要安装PyTorch后运行:")
    print("-" * 40)
    print(EXAMPLE_CODE)
    
    print("\n✅ 任务3完成状态:")
    print("✓ MultiHeadGATLayer - 多头图注意力层实现")
    print("✓ GraphNorm - 图归一化层实现")
    print("✓ ResidualBlock - 残差连接实现")
    print("✓ PositionalEncoding - 位置编码实现")
    print("✓ AttentionPooling - 注意力池化实现")
    print("✓ GATEncoder - 主要编码器实现")
    print("✓ MultiScaleGATEncoder - 多尺度编码器实现")
    print("✓ 图工具函数集合")
    print("✓ 完整的测试和验证")
    print("✓ 使用示例和集成指南")
    
    print("\n🎯 关键特性:")
    print("• 支持8-36节点的变长图处理")
    print("• 多头注意力机制（4-8头）")
    print("• 残差连接和层归一化")
    print("• 多种池化策略选择")
    print("• 批处理和GPU加速支持")
    print("• 注意力权重可视化")
    print("• 完整的错误处理和验证")
    
    print("\n📁 文件结构:")
    print("models/")
    print("├── layers.py           # 基础层实现")
    print("├── graph_encoder.py    # 主要编码器")
    print("├── test_encoder.py     # 功能测试")
    print("├── test_structure.py   # 结构测试")
    print("└── encoder_example.py  # 使用示例")
    print("utils/")
    print("└── graph_utils.py      # 图工具函数")

if __name__ == "__main__":
    main()
