"""
Lattice GNN-Diffusion: Graph Neural Network with Diffusion Models 
for Lattice Structure Prediction and Generation

This package provides a unified framework for:
1. Predicting lattice structure performance from geometric features
2. Generating lattice structures based on performance requirements

Key Components:
- Graph Neural Network encoder (GAT-based)
- Performance prediction module
- Conditional diffusion model for structure generation
- Physical constraint validation
"""

__version__ = "0.1.0"
__author__ = "AI Assistant"
__email__ = "<EMAIL>"

from .models import *
from .data import *
from .training import *
from .utils import *
