"""
性能预测模块：基于图编码器的多目标回归预测
支持动态维度的性能指标预测
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, List, Dict, Any, Tuple
import numpy as np

class MLPPredictor(nn.Module):
    """多层感知机预测器"""
    
    def __init__(self, input_dim: int, output_dim: int, 
                 hidden_dims: List[int] = [256, 128],
                 activation: str = 'relu', dropout: float = 0.1,
                 use_batch_norm: bool = True, use_layer_norm: bool = False,
                 final_activation: Optional[str] = None):
        """
        初始化MLP预测器
        
        Args:
            input_dim: 输入维度（图嵌入维度）
            output_dim: 输出维度（性能指标数量）
            hidden_dims: 隐藏层维度列表
            activation: 激活函数类型
            dropout: Dropout概率
            use_batch_norm: 是否使用批归一化
            use_layer_norm: 是否使用层归一化
            final_activation: 最终层激活函数
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.hidden_dims = hidden_dims
        self.activation = activation
        self.dropout = dropout
        self.use_batch_norm = use_batch_norm
        self.use_layer_norm = use_layer_norm
        self.final_activation = final_activation
        
        # 构建网络层
        self.layers = nn.ModuleList()
        self.norms = nn.ModuleList()
        self.dropouts = nn.ModuleList()
        
        # 输入层到第一个隐藏层
        dims = [input_dim] + hidden_dims + [output_dim]
        
        for i in range(len(dims) - 1):
            # 线性层
            self.layers.append(nn.Linear(dims[i], dims[i + 1]))
            
            # 归一化层（除了最后一层）
            if i < len(dims) - 2:
                if use_batch_norm:
                    self.norms.append(nn.BatchNorm1d(dims[i + 1]))
                elif use_layer_norm:
                    self.norms.append(nn.LayerNorm(dims[i + 1]))
                else:
                    self.norms.append(nn.Identity())
                
                # Dropout层
                self.dropouts.append(nn.Dropout(dropout))
            else:
                self.norms.append(nn.Identity())
                self.dropouts.append(nn.Identity())
        
        # 激活函数
        self.act_fn = self._get_activation(activation)
        self.final_act_fn = self._get_activation(final_activation) if final_activation else None
        
        # 初始化参数
        self.reset_parameters()
    
    def _get_activation(self, activation: str) -> nn.Module:
        """获取激活函数"""
        if activation == 'relu':
            return nn.ReLU()
        elif activation == 'gelu':
            return nn.GELU()
        elif activation == 'leaky_relu':
            return nn.LeakyReLU(0.2)
        elif activation == 'elu':
            return nn.ELU()
        elif activation == 'swish':
            return nn.SiLU()
        elif activation == 'tanh':
            return nn.Tanh()
        elif activation == 'sigmoid':
            return nn.Sigmoid()
        else:
            raise ValueError(f"Unknown activation: {activation}")
    
    def reset_parameters(self):
        """重置参数"""
        for layer in self.layers:
            if isinstance(layer, nn.Linear):
                nn.init.xavier_uniform_(layer.weight)
                nn.init.zeros_(layer.bias)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入图嵌入 [batch_size, input_dim]
            
        Returns:
            性能预测 [batch_size, output_dim]
        """
        for i, (layer, norm, dropout) in enumerate(zip(self.layers, self.norms, self.dropouts)):
            x = layer(x)
            
            # 最后一层特殊处理
            if i == len(self.layers) - 1:
                if self.final_act_fn is not None:
                    x = self.final_act_fn(x)
            else:
                x = norm(x)
                x = self.act_fn(x)
                x = dropout(x)
        
        return x

class PerformancePredictor(nn.Module):
    """性能预测模块"""
    
    def __init__(self, graph_encoder: nn.Module, num_performance_features: int,
                 predictor_config: Optional[Dict[str, Any]] = None,
                 freeze_encoder: bool = False):
        """
        初始化性能预测模块
        
        Args:
            graph_encoder: 图编码器
            num_performance_features: 性能特征数量（动态确定）
            predictor_config: 预测器配置
            freeze_encoder: 是否冻结编码器参数
        """
        super().__init__()
        
        self.graph_encoder = graph_encoder
        self.num_performance_features = num_performance_features
        self.freeze_encoder = freeze_encoder
        
        # 冻结编码器参数
        if freeze_encoder:
            for param in self.graph_encoder.parameters():
                param.requires_grad = False
        
        # 获取编码器输出维度
        encoder_output_dim = self._get_encoder_output_dim()
        
        # 默认预测器配置
        default_config = {
            'hidden_dims': [256, 128],
            'activation': 'relu',
            'dropout': 0.1,
            'use_batch_norm': True,
            'use_layer_norm': False,
            'final_activation': None
        }
        
        if predictor_config:
            default_config.update(predictor_config)
        
        # 创建预测器
        self.predictor = MLPPredictor(
            input_dim=encoder_output_dim,
            output_dim=num_performance_features,
            **default_config
        )
        
        print(f"性能预测器初始化完成:")
        print(f"  编码器输出维度: {encoder_output_dim}")
        print(f"  性能特征数量: {num_performance_features}")
        print(f"  预测器隐藏层: {default_config['hidden_dims']}")
        print(f"  编码器冻结: {freeze_encoder}")
    
    def _get_encoder_output_dim(self) -> int:
        """获取编码器输出维度"""
        # 创建虚拟输入来推断输出维度
        with torch.no_grad():
            dummy_x = torch.randn(10, 3)  # 10个节点，3维坐标
            dummy_edge_index = torch.randint(0, 10, (2, 20))  # 20条边
            dummy_mask = torch.ones(10, dtype=torch.bool)
            
            try:
                output = self.graph_encoder(dummy_x, dummy_edge_index, node_mask=dummy_mask)
                return output.shape[-1]
            except Exception as e:
                print(f"无法自动推断编码器输出维度: {e}")
                # 默认值
                return 256
    
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor,
                batch: Optional[torch.Tensor] = None,
                node_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 节点特征 [num_nodes, 3]
            edge_index: 边索引 [2, num_edges]
            batch: 批索引 [num_nodes]
            node_mask: 节点mask [num_nodes]
            
        Returns:
            性能预测 [batch_size, num_performance_features]
        """
        # 图编码
        if self.freeze_encoder:
            with torch.no_grad():
                graph_embedding = self.graph_encoder(x, edge_index, batch, node_mask)
        else:
            graph_embedding = self.graph_encoder(x, edge_index, batch, node_mask)
        
        # 性能预测
        performance_pred = self.predictor(graph_embedding)
        
        return performance_pred
    
    def predict_with_uncertainty(self, x: torch.Tensor, edge_index: torch.Tensor,
                                batch: Optional[torch.Tensor] = None,
                                node_mask: Optional[torch.Tensor] = None,
                                num_samples: int = 10) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        带不确定性的预测（使用Dropout采样）
        
        Args:
            x: 节点特征
            edge_index: 边索引
            batch: 批索引
            node_mask: 节点mask
            num_samples: 采样次数
            
        Returns:
            (预测均值, 预测标准差)
        """
        self.train()  # 启用Dropout
        
        predictions = []
        for _ in range(num_samples):
            with torch.no_grad():
                pred = self.forward(x, edge_index, batch, node_mask)
                predictions.append(pred)
        
        predictions = torch.stack(predictions)  # [num_samples, batch_size, num_features]
        
        mean_pred = predictions.mean(dim=0)
        std_pred = predictions.std(dim=0)
        
        self.eval()  # 恢复评估模式
        
        return mean_pred, std_pred
    
    def get_feature_importance(self, x: torch.Tensor, edge_index: torch.Tensor,
                             batch: Optional[torch.Tensor] = None,
                             node_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        获取特征重要性（基于梯度）
        
        Args:
            x: 节点特征
            edge_index: 边索引
            batch: 批索引
            node_mask: 节点mask
            
        Returns:
            特征重要性 [num_performance_features]
        """
        x.requires_grad_(True)
        
        # 前向传播
        output = self.forward(x, edge_index, batch, node_mask)
        
        # 计算梯度
        importance = []
        for i in range(output.shape[-1]):
            grad = torch.autograd.grad(
                outputs=output[:, i].sum(),
                inputs=x,
                create_graph=False,
                retain_graph=True
            )[0]
            importance.append(grad.abs().mean().item())
        
        return torch.tensor(importance)

class AdaptivePerformancePredictor(PerformancePredictor):
    """自适应性能预测器（支持动态特征数量）"""
    
    def __init__(self, graph_encoder: nn.Module, 
                 initial_num_features: int = 18,
                 predictor_config: Optional[Dict[str, Any]] = None,
                 freeze_encoder: bool = False):
        """
        初始化自适应性能预测器
        
        Args:
            graph_encoder: 图编码器
            initial_num_features: 初始特征数量
            predictor_config: 预测器配置
            freeze_encoder: 是否冻结编码器
        """
        super().__init__(graph_encoder, initial_num_features, predictor_config, freeze_encoder)
        
        self.feature_registry = {}  # 特征名称注册表
        self.current_features = []  # 当前特征列表
    
    def update_output_dimension(self, new_num_features: int, feature_names: Optional[List[str]] = None):
        """
        更新输出维度
        
        Args:
            new_num_features: 新的特征数量
            feature_names: 特征名称列表
        """
        if new_num_features == self.num_performance_features:
            return  # 无需更新
        
        print(f"更新性能预测器输出维度: {self.num_performance_features} -> {new_num_features}")
        
        # 保存旧的预测器权重
        old_predictor = self.predictor
        
        # 创建新的预测器
        encoder_output_dim = self._get_encoder_output_dim()
        predictor_config = {
            'hidden_dims': old_predictor.hidden_dims,
            'activation': old_predictor.activation,
            'dropout': old_predictor.dropout,
            'use_batch_norm': old_predictor.use_batch_norm,
            'use_layer_norm': old_predictor.use_layer_norm,
            'final_activation': old_predictor.final_activation
        }
        
        self.predictor = MLPPredictor(
            input_dim=encoder_output_dim,
            output_dim=new_num_features,
            **predictor_config
        )
        
        # 尝试迁移权重（隐藏层）
        self._transfer_weights(old_predictor, self.predictor)
        
        # 更新特征信息
        self.num_performance_features = new_num_features
        if feature_names:
            self.current_features = feature_names
            for i, name in enumerate(feature_names):
                self.feature_registry[name] = i
        
        print(f"权重迁移完成，新预测器参数数量: {sum(p.numel() for p in self.predictor.parameters())}")
    
    def _transfer_weights(self, old_predictor: MLPPredictor, new_predictor: MLPPredictor):
        """迁移权重"""
        try:
            # 迁移隐藏层权重
            for i, (old_layer, new_layer) in enumerate(zip(old_predictor.layers[:-1], new_predictor.layers[:-1])):
                if old_layer.weight.shape == new_layer.weight.shape:
                    new_layer.weight.data.copy_(old_layer.weight.data)
                    new_layer.bias.data.copy_(old_layer.bias.data)
                    print(f"迁移第{i+1}层权重: {old_layer.weight.shape}")
            
            # 迁移归一化层权重
            for i, (old_norm, new_norm) in enumerate(zip(old_predictor.norms[:-1], new_predictor.norms[:-1])):
                if hasattr(old_norm, 'weight') and hasattr(new_norm, 'weight'):
                    if old_norm.weight.shape == new_norm.weight.shape:
                        new_norm.weight.data.copy_(old_norm.weight.data)
                        new_norm.bias.data.copy_(old_norm.bias.data)
                        print(f"迁移第{i+1}层归一化权重")
        
        except Exception as e:
            print(f"权重迁移部分失败: {e}")
    
    def get_feature_names(self) -> List[str]:
        """获取当前特征名称"""
        return self.current_features.copy()
    
    def get_feature_index(self, feature_name: str) -> int:
        """获取特征索引"""
        return self.feature_registry.get(feature_name, -1)
