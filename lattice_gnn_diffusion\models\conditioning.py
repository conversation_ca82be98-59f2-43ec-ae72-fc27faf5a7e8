"""
条件机制：处理性能需求条件的编码和注入
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, List, Dict, Any, Tuple, Union

class PerformanceConditioner(nn.Module):
    """性能条件编码器：将性能需求编码为条件向量"""
    
    def __init__(self, performance_dim: int, condition_dim: int = 256,
                 encoding_type: str = 'mlp', 
                 mlp_config: Optional[Dict[str, Any]] = None,
                 embedding_config: Optional[Dict[str, Any]] = None):
        """
        初始化性能条件编码器
        
        Args:
            performance_dim: 性能特征维度
            condition_dim: 条件编码维度
            encoding_type: 编码类型 ('mlp', 'embedding', 'hybrid')
            mlp_config: MLP配置
            embedding_config: 嵌入配置
        """
        super().__init__()
        
        self.performance_dim = performance_dim
        self.condition_dim = condition_dim
        self.encoding_type = encoding_type
        
        if encoding_type == 'mlp':
            self._build_mlp_encoder(mlp_config)
        elif encoding_type == 'embedding':
            self._build_embedding_encoder(embedding_config)
        elif encoding_type == 'hybrid':
            self._build_hybrid_encoder(mlp_config, embedding_config)
        else:
            raise ValueError(f"Unknown encoding type: {encoding_type}")
        
        print(f"性能条件编码器初始化完成:")
        print(f"  性能维度: {performance_dim}")
        print(f"  条件维度: {condition_dim}")
        print(f"  编码类型: {encoding_type}")
    
    def _build_mlp_encoder(self, config: Optional[Dict[str, Any]]):
        """构建MLP编码器"""
        default_config = {
            'hidden_dims': [512, 256],
            'activation': 'relu',
            'dropout': 0.1,
            'use_batch_norm': True
        }
        if config:
            default_config.update(config)
        
        layers = []
        input_dim = self.performance_dim
        
        for hidden_dim in default_config['hidden_dims']:
            layers.append(nn.Linear(input_dim, hidden_dim))
            
            if default_config['use_batch_norm']:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            if default_config['activation'] == 'relu':
                layers.append(nn.ReLU())
            elif default_config['activation'] == 'gelu':
                layers.append(nn.GELU())
            elif default_config['activation'] == 'silu':
                layers.append(nn.SiLU())
            
            layers.append(nn.Dropout(default_config['dropout']))
            input_dim = hidden_dim
        
        layers.append(nn.Linear(input_dim, self.condition_dim))
        
        self.encoder = nn.Sequential(*layers)
    
    def _build_embedding_encoder(self, config: Optional[Dict[str, Any]]):
        """构建嵌入编码器（用于离散化的性能值）"""
        default_config = {
            'num_bins': 100,  # 每个性能指标的离散化bin数
            'embed_dim': 64,  # 每个指标的嵌入维度
            'combine_method': 'concat'  # 'concat', 'sum', 'mean'
        }
        if config:
            default_config.update(config)
        
        self.num_bins = default_config['num_bins']
        self.embed_dim = default_config['embed_dim']
        self.combine_method = default_config['combine_method']
        
        # 为每个性能指标创建嵌入层
        self.embeddings = nn.ModuleList([
            nn.Embedding(self.num_bins, self.embed_dim)
            for _ in range(self.performance_dim)
        ])
        
        # 组合层
        if self.combine_method == 'concat':
            combined_dim = self.performance_dim * self.embed_dim
        else:
            combined_dim = self.embed_dim
        
        self.projection = nn.Linear(combined_dim, self.condition_dim)
    
    def _build_hybrid_encoder(self, mlp_config: Optional[Dict[str, Any]], 
                             embedding_config: Optional[Dict[str, Any]]):
        """构建混合编码器"""
        # 连续值MLP分支
        self._build_mlp_encoder(mlp_config)
        self.continuous_encoder = self.encoder
        
        # 离散值嵌入分支
        self._build_embedding_encoder(embedding_config)
        
        # 融合层
        fusion_input_dim = self.condition_dim + self.condition_dim
        self.fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, self.condition_dim),
            nn.ReLU(),
            nn.Linear(self.condition_dim, self.condition_dim)
        )
    
    def forward(self, performance: torch.Tensor, 
                performance_ranges: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            performance: 性能值 [batch_size, performance_dim]
            performance_ranges: 性能范围 [performance_dim, 2] (min, max)
            
        Returns:
            条件编码 [batch_size, condition_dim]
        """
        if self.encoding_type == 'mlp':
            return self.encoder(performance)
        
        elif self.encoding_type == 'embedding':
            return self._embedding_forward(performance, performance_ranges)
        
        elif self.encoding_type == 'hybrid':
            # 连续分支
            continuous_encoding = self.continuous_encoder(performance)
            
            # 离散分支
            discrete_encoding = self._embedding_forward(performance, performance_ranges)
            
            # 融合
            combined = torch.cat([continuous_encoding, discrete_encoding], dim=-1)
            return self.fusion(combined)
    
    def _embedding_forward(self, performance: torch.Tensor, 
                          performance_ranges: Optional[torch.Tensor]) -> torch.Tensor:
        """嵌入编码前向传播"""
        batch_size = performance.shape[0]
        device = performance.device
        
        # 离散化性能值
        if performance_ranges is not None:
            # 使用提供的范围
            normalized = (performance - performance_ranges[:, 0]) / (performance_ranges[:, 1] - performance_ranges[:, 0])
        else:
            # 使用数据的最小最大值
            normalized = (performance - performance.min(dim=0)[0]) / (performance.max(dim=0)[0] - performance.min(dim=0)[0])
        
        # 转换为bin索引
        bin_indices = (normalized * (self.num_bins - 1)).long().clamp(0, self.num_bins - 1)
        
        # 嵌入每个性能指标
        embeddings = []
        for i in range(self.performance_dim):
            embed = self.embeddings[i](bin_indices[:, i])  # [batch_size, embed_dim]
            embeddings.append(embed)
        
        # 组合嵌入
        if self.combine_method == 'concat':
            combined = torch.cat(embeddings, dim=-1)
        elif self.combine_method == 'sum':
            combined = torch.stack(embeddings, dim=1).sum(dim=1)
        elif self.combine_method == 'mean':
            combined = torch.stack(embeddings, dim=1).mean(dim=1)
        
        return self.projection(combined)

class AdaptiveConditioner(nn.Module):
    """自适应条件编码器：支持动态性能维度"""
    
    def __init__(self, max_performance_dim: int = 50, condition_dim: int = 256,
                 base_encoder_config: Optional[Dict[str, Any]] = None):
        """
        初始化自适应条件编码器
        
        Args:
            max_performance_dim: 最大性能维度
            condition_dim: 条件编码维度
            base_encoder_config: 基础编码器配置
        """
        super().__init__()
        
        self.max_performance_dim = max_performance_dim
        self.condition_dim = condition_dim
        self.current_performance_dim = max_performance_dim
        
        # 创建基础编码器
        self.base_encoder = PerformanceConditioner(
            performance_dim=max_performance_dim,
            condition_dim=condition_dim,
            **(base_encoder_config or {})
        )
        
        # 维度适配层
        self.dimension_adapters = nn.ModuleDict()
        
        print(f"自适应条件编码器初始化完成:")
        print(f"  最大性能维度: {max_performance_dim}")
        print(f"  条件维度: {condition_dim}")
    
    def update_performance_dimension(self, new_dim: int):
        """
        更新性能维度
        
        Args:
            new_dim: 新的性能维度
        """
        if new_dim == self.current_performance_dim:
            return
        
        print(f"更新性能维度: {self.current_performance_dim} -> {new_dim}")
        
        # 创建或获取适配器
        adapter_key = str(new_dim)
        if adapter_key not in self.dimension_adapters:
            if new_dim <= self.max_performance_dim:
                # 使用投影层适配
                self.dimension_adapters[adapter_key] = nn.Linear(new_dim, self.max_performance_dim)
            else:
                # 需要扩展，使用更复杂的适配器
                self.dimension_adapters[adapter_key] = nn.Sequential(
                    nn.Linear(new_dim, self.max_performance_dim * 2),
                    nn.ReLU(),
                    nn.Linear(self.max_performance_dim * 2, self.max_performance_dim)
                )
        
        self.current_performance_dim = new_dim
    
    def forward(self, performance: torch.Tensor, 
                performance_ranges: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            performance: 性能值 [batch_size, current_performance_dim]
            performance_ranges: 性能范围
            
        Returns:
            条件编码 [batch_size, condition_dim]
        """
        current_dim = performance.shape[-1]
        
        if current_dim != self.current_performance_dim:
            self.update_performance_dimension(current_dim)
        
        # 适配维度
        if current_dim != self.max_performance_dim:
            adapter_key = str(current_dim)
            adapted_performance = self.dimension_adapters[adapter_key](performance)
        else:
            adapted_performance = performance
        
        # 编码
        return self.base_encoder(adapted_performance, performance_ranges)

class ConditionalCrossAttention(nn.Module):
    """条件交叉注意力：将条件信息注入到特征中"""
    
    def __init__(self, feature_dim: int, condition_dim: int, 
                 num_heads: int = 8, dropout: float = 0.1):
        """
        初始化条件交叉注意力
        
        Args:
            feature_dim: 特征维度
            condition_dim: 条件维度
            num_heads: 注意力头数
            dropout: Dropout概率
        """
        super().__init__()
        
        self.feature_dim = feature_dim
        self.condition_dim = condition_dim
        self.num_heads = num_heads
        
        # 确保维度可以被头数整除
        assert feature_dim % num_heads == 0
        
        self.head_dim = feature_dim // num_heads
        
        # 查询、键、值投影
        self.q_proj = nn.Linear(feature_dim, feature_dim)
        self.k_proj = nn.Linear(condition_dim, feature_dim)
        self.v_proj = nn.Linear(condition_dim, feature_dim)
        
        # 输出投影
        self.out_proj = nn.Linear(feature_dim, feature_dim)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 层归一化
        self.norm = nn.LayerNorm(feature_dim)
        
        # 缩放因子
        self.scale = self.head_dim ** -0.5
    
    def forward(self, features: torch.Tensor, condition: torch.Tensor,
                feature_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            features: 特征 [batch_size, seq_len, feature_dim]
            condition: 条件 [batch_size, condition_dim]
            feature_mask: 特征mask [batch_size, seq_len]
            
        Returns:
            注入条件后的特征 [batch_size, seq_len, feature_dim]
        """
        batch_size, seq_len, _ = features.shape
        
        # 扩展条件到序列长度
        condition_expanded = condition.unsqueeze(1).expand(-1, seq_len, -1)
        
        # 计算查询、键、值
        Q = self.q_proj(features)  # [batch_size, seq_len, feature_dim]
        K = self.k_proj(condition_expanded)  # [batch_size, seq_len, feature_dim]
        V = self.v_proj(condition_expanded)  # [batch_size, seq_len, feature_dim]
        
        # 重塑为多头
        Q = Q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) * self.scale
        
        # 应用mask
        if feature_mask is not None:
            mask = feature_mask.unsqueeze(1).unsqueeze(1)  # [batch_size, 1, 1, seq_len]
            scores = scores.masked_fill(~mask, float('-inf'))
        
        # Softmax
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力
        attn_output = torch.matmul(attn_weights, V)
        
        # 重塑回原始形状
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.feature_dim
        )
        
        # 输出投影
        output = self.out_proj(attn_output)
        
        # 残差连接和层归一化
        output = self.norm(features + output)
        
        return output

class MultiScaleConditioner(nn.Module):
    """多尺度条件编码器：在不同尺度上编码条件信息"""
    
    def __init__(self, performance_dim: int, condition_dims: List[int] = [64, 128, 256],
                 fusion_method: str = 'concat'):
        """
        初始化多尺度条件编码器
        
        Args:
            performance_dim: 性能维度
            condition_dims: 不同尺度的条件维度
            fusion_method: 融合方法 ('concat', 'sum', 'attention')
        """
        super().__init__()
        
        self.performance_dim = performance_dim
        self.condition_dims = condition_dims
        self.fusion_method = fusion_method
        
        # 创建多个尺度的编码器
        self.encoders = nn.ModuleList([
            PerformanceConditioner(
                performance_dim=performance_dim,
                condition_dim=dim,
                encoding_type='mlp'
            )
            for dim in condition_dims
        ])
        
        # 融合层
        if fusion_method == 'concat':
            self.fusion_dim = sum(condition_dims)
            self.fusion = nn.Identity()
        elif fusion_method == 'sum':
            assert all(dim == condition_dims[0] for dim in condition_dims), "所有维度必须相同才能求和"
            self.fusion_dim = condition_dims[0]
            self.fusion = lambda x: torch.stack(x, dim=0).sum(dim=0)
        elif fusion_method == 'attention':
            self.fusion_dim = condition_dims[-1]  # 使用最大维度
            self.attention_weights = nn.Parameter(torch.ones(len(condition_dims)))
            self.fusion = self._attention_fusion
        
        print(f"多尺度条件编码器初始化完成:")
        print(f"  性能维度: {performance_dim}")
        print(f"  条件维度: {condition_dims}")
        print(f"  融合方法: {fusion_method}")
        print(f"  输出维度: {self.fusion_dim}")
    
    def _attention_fusion(self, encodings: List[torch.Tensor]) -> torch.Tensor:
        """注意力融合"""
        # 归一化注意力权重
        weights = F.softmax(self.attention_weights, dim=0)
        
        # 投影到相同维度
        projected = []
        for i, encoding in enumerate(encodings):
            if encoding.shape[-1] != self.fusion_dim:
                proj = nn.Linear(encoding.shape[-1], self.fusion_dim).to(encoding.device)
                projected.append(proj(encoding))
            else:
                projected.append(encoding)
        
        # 加权求和
        weighted_sum = sum(w * enc for w, enc in zip(weights, projected))
        return weighted_sum
    
    def forward(self, performance: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            performance: 性能值 [batch_size, performance_dim]
            
        Returns:
            多尺度条件编码 [batch_size, fusion_dim]
        """
        # 在不同尺度编码
        encodings = [encoder(performance) for encoder in self.encoders]
        
        # 融合
        if self.fusion_method == 'concat':
            return torch.cat(encodings, dim=-1)
        else:
            return self.fusion(encodings)
