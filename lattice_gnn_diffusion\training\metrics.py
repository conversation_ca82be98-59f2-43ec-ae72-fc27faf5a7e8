"""
评估指标：性能预测模型的评估指标和分析工具
"""

import torch
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import matplotlib.pyplot as plt
import seaborn as sns

class PerformanceMetrics:
    """性能评估指标计算器"""
    
    def __init__(self, feature_names: Optional[List[str]] = None):
        """
        初始化评估指标
        
        Args:
            feature_names: 特征名称列表
        """
        self.feature_names = feature_names or []
        self.reset()
    
    def reset(self):
        """重置累积统计"""
        self.predictions = []
        self.targets = []
        self.losses = []
    
    def update(self, pred: torch.Tensor, target: torch.Tensor, loss: Optional[torch.Tensor] = None):
        """
        更新统计信息
        
        Args:
            pred: 预测值 [batch_size, num_features]
            target: 目标值 [batch_size, num_features]
            loss: 损失值
        """
        # 转换为numpy
        pred_np = pred.detach().cpu().numpy()
        target_np = target.detach().cpu().numpy()
        
        self.predictions.append(pred_np)
        self.targets.append(target_np)
        
        if loss is not None:
            self.losses.append(loss.detach().cpu().item())
    
    def compute_metrics(self) -> Dict[str, Any]:
        """计算所有评估指标"""
        if not self.predictions:
            return {}
        
        # 合并所有预测和目标
        all_pred = np.concatenate(self.predictions, axis=0)  # [total_samples, num_features]
        all_target = np.concatenate(self.targets, axis=0)
        
        metrics = {}
        
        # 整体指标
        metrics.update(self._compute_overall_metrics(all_pred, all_target))
        
        # 分特征指标
        metrics.update(self._compute_per_feature_metrics(all_pred, all_target))
        
        # 损失统计
        if self.losses:
            metrics['avg_loss'] = np.mean(self.losses)
            metrics['loss_std'] = np.std(self.losses)
        
        return metrics
    
    def _compute_overall_metrics(self, pred: np.ndarray, target: np.ndarray) -> Dict[str, float]:
        """计算整体指标"""
        metrics = {}
        
        # R²决定系数
        try:
            r2 = r2_score(target.flatten(), pred.flatten())
            metrics['r2_overall'] = r2
        except:
            metrics['r2_overall'] = float('nan')
        
        # 均方误差
        mse = mean_squared_error(target.flatten(), pred.flatten())
        metrics['mse_overall'] = mse
        metrics['rmse_overall'] = np.sqrt(mse)
        
        # 平均绝对误差
        mae = mean_absolute_error(target.flatten(), pred.flatten())
        metrics['mae_overall'] = mae
        
        # 平均绝对百分比误差
        mape = self._compute_mape(target.flatten(), pred.flatten())
        metrics['mape_overall'] = mape
        
        # 相关系数
        try:
            corr = np.corrcoef(target.flatten(), pred.flatten())[0, 1]
            metrics['correlation_overall'] = corr
        except:
            metrics['correlation_overall'] = float('nan')
        
        return metrics
    
    def _compute_per_feature_metrics(self, pred: np.ndarray, target: np.ndarray) -> Dict[str, Any]:
        """计算分特征指标"""
        metrics = {}
        num_features = pred.shape[1]
        
        # 各特征的指标
        r2_scores = []
        mse_scores = []
        mae_scores = []
        mape_scores = []
        corr_scores = []
        
        for i in range(num_features):
            feature_name = self.feature_names[i] if i < len(self.feature_names) else f'feature_{i}'
            
            pred_i = pred[:, i]
            target_i = target[:, i]
            
            # R²
            try:
                r2_i = r2_score(target_i, pred_i)
                r2_scores.append(r2_i)
                metrics[f'r2_{feature_name}'] = r2_i
            except:
                r2_scores.append(float('nan'))
                metrics[f'r2_{feature_name}'] = float('nan')
            
            # MSE
            mse_i = mean_squared_error(target_i, pred_i)
            mse_scores.append(mse_i)
            metrics[f'mse_{feature_name}'] = mse_i
            metrics[f'rmse_{feature_name}'] = np.sqrt(mse_i)
            
            # MAE
            mae_i = mean_absolute_error(target_i, pred_i)
            mae_scores.append(mae_i)
            metrics[f'mae_{feature_name}'] = mae_i
            
            # MAPE
            mape_i = self._compute_mape(target_i, pred_i)
            mape_scores.append(mape_i)
            metrics[f'mape_{feature_name}'] = mape_i
            
            # 相关系数
            try:
                corr_i = np.corrcoef(target_i, pred_i)[0, 1]
                corr_scores.append(corr_i)
                metrics[f'correlation_{feature_name}'] = corr_i
            except:
                corr_scores.append(float('nan'))
                metrics[f'correlation_{feature_name}'] = float('nan')
        
        # 统计摘要
        metrics['r2_mean'] = np.nanmean(r2_scores)
        metrics['r2_std'] = np.nanstd(r2_scores)
        metrics['r2_min'] = np.nanmin(r2_scores)
        metrics['r2_max'] = np.nanmax(r2_scores)
        
        metrics['mse_mean'] = np.mean(mse_scores)
        metrics['mae_mean'] = np.mean(mae_scores)
        metrics['mape_mean'] = np.nanmean(mape_scores)
        metrics['correlation_mean'] = np.nanmean(corr_scores)
        
        # 保存详细分数
        metrics['r2_per_feature'] = r2_scores
        metrics['mse_per_feature'] = mse_scores
        metrics['mae_per_feature'] = mae_scores
        metrics['mape_per_feature'] = mape_scores
        metrics['correlation_per_feature'] = corr_scores
        
        return metrics
    
    def _compute_mape(self, target: np.ndarray, pred: np.ndarray, epsilon: float = 1e-8) -> float:
        """计算平均绝对百分比误差"""
        # 避免除零
        mask = np.abs(target) > epsilon
        if not np.any(mask):
            return float('nan')
        
        mape = np.mean(np.abs((target[mask] - pred[mask]) / target[mask])) * 100
        return mape
    
    def get_worst_predictions(self, top_k: int = 5) -> Dict[str, Any]:
        """获取最差的预测结果"""
        if not self.predictions:
            return {}
        
        all_pred = np.concatenate(self.predictions, axis=0)
        all_target = np.concatenate(self.targets, axis=0)
        
        # 计算每个样本的误差
        errors = np.mean((all_pred - all_target) ** 2, axis=1)  # MSE per sample
        
        # 找到最差的样本
        worst_indices = np.argsort(errors)[-top_k:][::-1]
        
        worst_predictions = {
            'indices': worst_indices.tolist(),
            'errors': errors[worst_indices].tolist(),
            'predictions': all_pred[worst_indices].tolist(),
            'targets': all_target[worst_indices].tolist()
        }
        
        return worst_predictions
    
    def get_best_predictions(self, top_k: int = 5) -> Dict[str, Any]:
        """获取最好的预测结果"""
        if not self.predictions:
            return {}
        
        all_pred = np.concatenate(self.predictions, axis=0)
        all_target = np.concatenate(self.targets, axis=0)
        
        # 计算每个样本的误差
        errors = np.mean((all_pred - all_target) ** 2, axis=1)
        
        # 找到最好的样本
        best_indices = np.argsort(errors)[:top_k]
        
        best_predictions = {
            'indices': best_indices.tolist(),
            'errors': errors[best_indices].tolist(),
            'predictions': all_pred[best_indices].tolist(),
            'targets': all_target[best_indices].tolist()
        }
        
        return best_predictions

class MetricsVisualizer:
    """指标可视化工具"""
    
    def __init__(self, feature_names: Optional[List[str]] = None):
        """
        初始化可视化工具
        
        Args:
            feature_names: 特征名称列表
        """
        self.feature_names = feature_names or []
    
    def plot_prediction_scatter(self, pred: np.ndarray, target: np.ndarray,
                              feature_indices: Optional[List[int]] = None,
                              save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制预测vs真实值散点图
        
        Args:
            pred: 预测值 [num_samples, num_features]
            target: 真实值 [num_samples, num_features]
            feature_indices: 要绘制的特征索引
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        if feature_indices is None:
            feature_indices = list(range(min(pred.shape[1], 6)))  # 最多显示6个特征
        
        num_features = len(feature_indices)
        cols = min(3, num_features)
        rows = (num_features + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        if num_features == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, feature_idx in enumerate(feature_indices):
            row = i // cols
            col = i % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            
            feature_name = (self.feature_names[feature_idx] 
                          if feature_idx < len(self.feature_names) 
                          else f'Feature {feature_idx}')
            
            # 散点图
            ax.scatter(target[:, feature_idx], pred[:, feature_idx], alpha=0.6)
            
            # 对角线
            min_val = min(target[:, feature_idx].min(), pred[:, feature_idx].min())
            max_val = max(target[:, feature_idx].max(), pred[:, feature_idx].max())
            ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
            
            # 计算R²
            try:
                r2 = r2_score(target[:, feature_idx], pred[:, feature_idx])
                ax.set_title(f'{feature_name}\nR² = {r2:.3f}')
            except:
                ax.set_title(f'{feature_name}\nR² = N/A')
            
            ax.set_xlabel('True Value')
            ax.set_ylabel('Predicted Value')
            ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(num_features, rows * cols):
            row = i // cols
            col = i % cols
            if rows > 1:
                axes[row, col].set_visible(False)
            else:
                axes[col].set_visible(False)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_feature_importance(self, metrics: Dict[str, Any],
                              metric_type: str = 'r2',
                              save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制特征重要性图
        
        Args:
            metrics: 评估指标字典
            metric_type: 指标类型 ('r2', 'mse', 'mae', 'correlation')
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        key = f'{metric_type}_per_feature'
        if key not in metrics:
            raise ValueError(f"Metric {key} not found in metrics")
        
        scores = metrics[key]
        feature_names = self.feature_names[:len(scores)] if self.feature_names else [f'F{i}' for i in range(len(scores))]
        
        # 过滤NaN值
        valid_indices = [i for i, score in enumerate(scores) if not np.isnan(score)]
        valid_scores = [scores[i] for i in valid_indices]
        valid_names = [feature_names[i] for i in valid_indices]
        
        if not valid_scores:
            print("No valid scores to plot")
            return None
        
        # 排序
        sorted_indices = np.argsort(valid_scores)[::-1]  # 降序
        sorted_scores = [valid_scores[i] for i in sorted_indices]
        sorted_names = [valid_names[i] for i in sorted_indices]
        
        # 绘图
        fig, ax = plt.subplots(figsize=(12, max(6, len(valid_scores) * 0.3)))
        
        bars = ax.barh(range(len(sorted_scores)), sorted_scores)
        ax.set_yticks(range(len(sorted_scores)))
        ax.set_yticklabels(sorted_names)
        ax.set_xlabel(f'{metric_type.upper()} Score')
        ax.set_title(f'Feature Performance ({metric_type.upper()})')
        
        # 添加数值标签
        for i, (bar, score) in enumerate(zip(bars, sorted_scores)):
            ax.text(bar.get_width() + 0.01 * max(sorted_scores), 
                   bar.get_y() + bar.get_height()/2, 
                   f'{score:.3f}', 
                   va='center', ha='left')
        
        ax.grid(True, alpha=0.3, axis='x')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_error_distribution(self, pred: np.ndarray, target: np.ndarray,
                              save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制误差分布图
        
        Args:
            pred: 预测值
            target: 真实值
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        errors = pred - target
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 整体误差分布
        axes[0, 0].hist(errors.flatten(), bins=50, alpha=0.7, edgecolor='black')
        axes[0, 0].set_title('Overall Error Distribution')
        axes[0, 0].set_xlabel('Prediction Error')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].axvline(0, color='red', linestyle='--', alpha=0.8)
        
        # 误差vs真实值
        axes[0, 1].scatter(target.flatten(), errors.flatten(), alpha=0.5)
        axes[0, 1].set_title('Error vs True Value')
        axes[0, 1].set_xlabel('True Value')
        axes[0, 1].set_ylabel('Prediction Error')
        axes[0, 1].axhline(0, color='red', linestyle='--', alpha=0.8)
        
        # 绝对误差vs真实值
        abs_errors = np.abs(errors)
        axes[1, 0].scatter(target.flatten(), abs_errors.flatten(), alpha=0.5)
        axes[1, 0].set_title('Absolute Error vs True Value')
        axes[1, 0].set_xlabel('True Value')
        axes[1, 0].set_ylabel('Absolute Error')
        
        # Q-Q图
        from scipy import stats
        stats.probplot(errors.flatten(), dist="norm", plot=axes[1, 1])
        axes[1, 1].set_title('Q-Q Plot (Normal Distribution)')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
