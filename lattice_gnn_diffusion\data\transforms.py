"""
数据变换和增强模块：为点阵结构数据提供各种变换操作
"""

import torch
import numpy as np
from torch_geometric.data import Data
from typing import Optional, Tuple, Union
import random

class RandomRotation:
    """随机旋转变换"""
    
    def __init__(self, max_angle: float = 30.0, axes: str = 'xyz'):
        """
        初始化随机旋转
        
        Args:
            max_angle: 最大旋转角度（度）
            axes: 旋转轴 ('x', 'y', 'z', 'xy', 'xyz' 等)
        """
        self.max_angle = max_angle
        self.axes = axes.lower()
    
    def __call__(self, data: Data) -> Data:
        """应用随机旋转"""
        if not hasattr(data, 'x') or data.x is None:
            return data
        
        # 只对有效节点进行旋转
        if hasattr(data, 'node_mask'):
            valid_nodes = data.node_mask
            coords = data.x[valid_nodes, :3]  # 假设前3维是坐标
        else:
            coords = data.x[:, :3]
            valid_nodes = torch.ones(data.x.shape[0], dtype=torch.bool)
        
        # 生成旋转矩阵
        rotation_matrix = self._generate_rotation_matrix()
        
        # 应用旋转
        rotated_coords = torch.matmul(coords, rotation_matrix.T)
        
        # 更新坐标
        new_x = data.x.clone()
        new_x[valid_nodes, :3] = rotated_coords
        
        data.x = new_x
        return data
    
    def _generate_rotation_matrix(self) -> torch.Tensor:
        """生成随机旋转矩阵"""
        rotation_matrix = torch.eye(3)
        
        for axis in self.axes:
            angle = random.uniform(-self.max_angle, self.max_angle) * np.pi / 180
            
            if axis == 'x':
                R = torch.tensor([
                    [1, 0, 0],
                    [0, np.cos(angle), -np.sin(angle)],
                    [0, np.sin(angle), np.cos(angle)]
                ], dtype=torch.float32)
            elif axis == 'y':
                R = torch.tensor([
                    [np.cos(angle), 0, np.sin(angle)],
                    [0, 1, 0],
                    [-np.sin(angle), 0, np.cos(angle)]
                ], dtype=torch.float32)
            elif axis == 'z':
                R = torch.tensor([
                    [np.cos(angle), -np.sin(angle), 0],
                    [np.sin(angle), np.cos(angle), 0],
                    [0, 0, 1]
                ], dtype=torch.float32)
            else:
                continue
            
            rotation_matrix = torch.matmul(rotation_matrix, R)
        
        return rotation_matrix

class RandomTranslation:
    """随机平移变换"""
    
    def __init__(self, max_translation: float = 0.1):
        """
        初始化随机平移
        
        Args:
            max_translation: 最大平移距离
        """
        self.max_translation = max_translation
    
    def __call__(self, data: Data) -> Data:
        """应用随机平移"""
        if not hasattr(data, 'x') or data.x is None:
            return data
        
        # 生成随机平移向量
        translation = torch.uniform(-self.max_translation, self.max_translation, (3,))
        
        # 只对有效节点进行平移
        if hasattr(data, 'node_mask'):
            valid_nodes = data.node_mask
            data.x[valid_nodes, :3] += translation
        else:
            data.x[:, :3] += translation
        
        return data

class AddGaussianNoise:
    """添加高斯噪声"""
    
    def __init__(self, noise_std: float = 0.01, coord_only: bool = True):
        """
        初始化噪声添加
        
        Args:
            noise_std: 噪声标准差
            coord_only: 是否只对坐标添加噪声
        """
        self.noise_std = noise_std
        self.coord_only = coord_only
    
    def __call__(self, data: Data) -> Data:
        """添加高斯噪声"""
        if not hasattr(data, 'x') or data.x is None:
            return data
        
        if self.coord_only:
            # 只对坐标（前3维）添加噪声
            if hasattr(data, 'node_mask'):
                valid_nodes = data.node_mask
                noise = torch.normal(0, self.noise_std, (valid_nodes.sum(), 3))
                data.x[valid_nodes, :3] += noise
            else:
                noise = torch.normal(0, self.noise_std, data.x[:, :3].shape)
                data.x[:, :3] += noise
        else:
            # 对所有特征添加噪声
            noise = torch.normal(0, self.noise_std, data.x.shape)
            data.x += noise
        
        return data

class RandomScale:
    """随机缩放变换"""
    
    def __init__(self, scale_range: Tuple[float, float] = (0.9, 1.1)):
        """
        初始化随机缩放
        
        Args:
            scale_range: 缩放范围 (min_scale, max_scale)
        """
        self.scale_range = scale_range
    
    def __call__(self, data: Data) -> Data:
        """应用随机缩放"""
        if not hasattr(data, 'x') or data.x is None:
            return data
        
        # 生成随机缩放因子
        scale = random.uniform(*self.scale_range)
        
        # 只对有效节点进行缩放
        if hasattr(data, 'node_mask'):
            valid_nodes = data.node_mask
            data.x[valid_nodes, :3] *= scale
        else:
            data.x[:, :3] *= scale
        
        return data

class CenterGraph:
    """图中心化变换"""
    
    def __call__(self, data: Data) -> Data:
        """将图中心化到原点"""
        if not hasattr(data, 'x') or data.x is None:
            return data
        
        # 计算有效节点的中心
        if hasattr(data, 'node_mask'):
            valid_nodes = data.node_mask
            coords = data.x[valid_nodes, :3]
        else:
            coords = data.x[:, :3]
            valid_nodes = torch.ones(data.x.shape[0], dtype=torch.bool)
        
        center = coords.mean(dim=0)
        
        # 中心化
        data.x[valid_nodes, :3] -= center
        
        return data

class NormalizeCoordinates:
    """坐标标准化变换"""
    
    def __init__(self, method: str = 'unit_sphere'):
        """
        初始化坐标标准化
        
        Args:
            method: 标准化方法 ('unit_sphere', 'unit_cube', 'zero_mean_unit_var')
        """
        self.method = method
    
    def __call__(self, data: Data) -> Data:
        """标准化坐标"""
        if not hasattr(data, 'x') or data.x is None:
            return data
        
        # 获取有效节点坐标
        if hasattr(data, 'node_mask'):
            valid_nodes = data.node_mask
            coords = data.x[valid_nodes, :3]
        else:
            coords = data.x[:, :3]
            valid_nodes = torch.ones(data.x.shape[0], dtype=torch.bool)
        
        if self.method == 'unit_sphere':
            # 标准化到单位球
            center = coords.mean(dim=0)
            coords_centered = coords - center
            max_dist = torch.norm(coords_centered, dim=1).max()
            if max_dist > 0:
                coords_normalized = coords_centered / max_dist
            else:
                coords_normalized = coords_centered
        
        elif self.method == 'unit_cube':
            # 标准化到单位立方体
            min_vals = coords.min(dim=0)[0]
            max_vals = coords.max(dim=0)[0]
            ranges = max_vals - min_vals
            ranges[ranges == 0] = 1  # 避免除零
            coords_normalized = (coords - min_vals) / ranges
        
        elif self.method == 'zero_mean_unit_var':
            # 零均值单位方差标准化
            mean = coords.mean(dim=0)
            std = coords.std(dim=0)
            std[std == 0] = 1  # 避免除零
            coords_normalized = (coords - mean) / std
        
        else:
            raise ValueError(f"未知的标准化方法: {self.method}")
        
        # 更新坐标
        data.x[valid_nodes, :3] = coords_normalized
        
        return data

class Compose:
    """组合多个变换"""
    
    def __init__(self, transforms):
        """
        初始化变换组合
        
        Args:
            transforms: 变换列表
        """
        self.transforms = transforms
    
    def __call__(self, data: Data) -> Data:
        """依次应用所有变换"""
        for transform in self.transforms:
            data = transform(data)
        return data

class RandomChoice:
    """随机选择一个变换应用"""
    
    def __init__(self, transforms, p=None):
        """
        初始化随机选择
        
        Args:
            transforms: 变换列表
            p: 每个变换的选择概率
        """
        self.transforms = transforms
        self.p = p
    
    def __call__(self, data: Data) -> Data:
        """随机选择一个变换应用"""
        if self.p is None:
            transform = random.choice(self.transforms)
        else:
            transform = np.random.choice(self.transforms, p=self.p)
        
        return transform(data)

# 预定义的变换组合
def get_training_transforms(noise_std: float = 0.01, rotation_angle: float = 15.0,
                          translation: float = 0.05) -> Compose:
    """获取训练时的数据增强变换"""
    return Compose([
        CenterGraph(),
        RandomRotation(max_angle=rotation_angle),
        RandomTranslation(max_translation=translation),
        AddGaussianNoise(noise_std=noise_std),
        NormalizeCoordinates(method='unit_sphere')
    ])

def get_validation_transforms() -> Compose:
    """获取验证时的变换（不包含随机性）"""
    return Compose([
        CenterGraph(),
        NormalizeCoordinates(method='unit_sphere')
    ])
